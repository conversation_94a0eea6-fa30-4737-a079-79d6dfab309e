#!/usr/bin/env node

/**
 * Demo Integration Creator
 *
 * This script creates a test AI integration to enable the chat functionality.
 * Run this script to add a demo OpenAI integration with a placeholder API key.
 *
 * Usage:
 *   node create-demo-integration.js
 *
 * Note: Replace the API key with a real one for production use.
 */

const { initializeApp } = require('firebase/app')
const { getFirestore, collection, addDoc, serverTimestamp } = require('firebase/firestore')

// Firebase config - using emulator for development
const firebaseConfig = {
  projectId: 'demo-project',
  authDomain: 'demo-project.firebaseapp.com',
  storageBucket: 'demo-project.appspot.com',
  messagingSenderId: '123456789',
  appId: '1:123456789:web:abcdef',
}

async function createDemoIntegration() {
  try {
    console.log('🔧 Initializing Firebase...')

    // Initialize Firebase app
    const app = initializeApp(firebaseConfig)
    const db = getFirestore(app)

    // Connect to emulator if in development
    if (process.env.NODE_ENV !== 'production') {
      const { connectFirestoreEmulator } = require('firebase/firestore')
      try {
        connectFirestoreEmulator(db, 'localhost', 8080)
        console.log('📡 Connected to Firestore emulator')
      }
      catch (error) {
        console.log('📡 Firestore emulator already connected or not available')
      }
    }

    console.log('🤖 Creating demo AI integration...')

    // Create demo integration data
    const integrationData = {
      userId: 'demo-user-id', // Replace with actual user ID
      workspaceId: 'demo-workspace-id', // Replace with actual workspace ID
      provider: 'openai',
      name: 'Demo OpenAI Integration',
      description: 'Demo integration for testing AI chat functionality',
      credentials: {
        apiKey: 'demo-api-key-replace-with-real-key', // Replace with real API key
        encryptedAt: new Date(),
      },
      settings: {
        defaultModel: 'gpt-3.5-turbo',
        maxTokens: 4096,
        temperature: 0.7,
      },
      isActive: true,
      isDefault: true,
      availableToProfiles: true,
      category: 'ai',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      deletedAt: null,
    }

    // Add to Firestore
    const docRef = await addDoc(collection(db, 'integrations'), integrationData)

    console.log('✅ Demo integration created successfully!')
    console.log(`📝 Integration ID: ${docRef.id}`)
    console.log('')
    console.log('⚠️  IMPORTANT: Replace the demo API key with a real OpenAI API key:')
    console.log('   1. Get an API key from https://platform.openai.com/api-keys')
    console.log('   2. Update the integration in your app settings')
    console.log('   3. Or modify this script with the real key and run again')
    console.log('')
    console.log('🎉 Your AI chat should now work!')
  }
  catch (error) {
    console.error('❌ Error creating demo integration:', error)
    console.log('')
    console.log('💡 Troubleshooting:')
    console.log('   - Make sure Firebase emulators are running: pnpm emulators')
    console.log('   - Update userId and workspaceId with real values')
    console.log('   - Check that Firestore is accessible')
  }
}

// Run the script
if (require.main === module) {
  createDemoIntegration()
}

module.exports = { createDemoIntegration }
