# Authentication Flow Documentation

## Overview

This document describes the standardized authentication flow for the PIB (Partners in Biz) application. The system uses Firebase Auth for authentication and Firestore for data storage, with a multi-workspace architecture.

## Key Principles

1. **Single User Identifier**: We use `userId` consistently throughout the system (not `ownerId`)
2. **Multi-Workspace Support**: Each user can have multiple workspaces
3. **Profile Per Workspace**: Each workspace has a dedicated profile for the user
4. **Session Management**: Sessions are stored in HTTP-only cookies and synchronized with the server

## Data Model

### Users Collection (`/users/{userId}`)
```typescript
{
  id: string          // Firebase Auth UID
  email: string
  username: string
  displayName?: string
  isActive: boolean
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

### Workspaces Collection (`/workspaces/{workspaceId}`)
```typescript
{
  id: string
  name: string
  slug: string
  description?: string
  type?: string       // personal, team, enterprise
  logoUrl?: string
  userId: string      // The user who owns this workspace
  createdBy: string   // The user who created this workspace
  profileId?: string  // Reference to shared profile (if using existing)
  createdAt: Timestamp
  updatedAt: Timestamp
  deletedAt?: Timestamp
}
```

### Workspace Members Collection (`/workspace_members/{workspaceId}_{userId}`)
```typescript
{
  workspaceId: string
  userId: string
  role: 'owner' | 'admin' | 'member'
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

### Profiles Collection (`/profiles/{userId}_{workspaceId}`)
```typescript
{
  userId: string
  workspaceId: string
  displayName: string
  bio?: string
  avatarUrl?: string
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

### Integrations Collection (`/integrations/{integrationId}`)
```typescript
{
  id: string
  workspaceId: string
  userId: string      // The user who created this integration
  provider: string
  name: string
  description?: string
  credentials: {
    apiKey: string
    encryptedAt: Date
  }
  settings: {
    defaultModel?: string
    [key: string]: any
  }
  isActive: boolean
  isDefault: boolean
  availableToProfiles: boolean
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

## Authentication Flow

### 1. Login Flow

```typescript
1. User enters email/password
2. Firebase Auth validates credentials
3. System fetches user document from Firestore
4. System fetches user's workspace memberships
5. System fetches workspace details for each membership
6. System sets the first workspace as current
7. System fetches the profile for current workspace
8. Session is saved to server via /api/auth/set
```

### 2. Signup Flow

```typescript
1. User enters email, username, password
2. Firebase Auth creates user account
3. System creates user document in Firestore
4. System creates default workspace
5. System creates workspace membership (as owner)
6. System creates profile for the workspace
7. Session is saved to server
```

### 3. Session Structure

```typescript
interface AuthState {
  user: {
    id: string
    email: string
    username: string
    token: {
      idToken: string
      // Other Firebase token fields
    }
  }
  currentWorkspace: Workspace | null
  currentProfile: Profile | null
  workspaces: Workspace[]
  isAuthenticated: boolean
}
```

## Security Rules

### Firestore Security Rules

```javascript
// Users can only read/write their own user document
match /users/{userId} {
  allow read, write: if request.auth.uid == userId;
}

// Workspaces are readable by authenticated users
// Only the workspace owner can create/update
match /workspaces/{workspaceId} {
  allow read: if request.auth != null;
  allow create: if request.auth.uid == request.resource.data.userId;
  allow update: if request.auth.uid == resource.data.userId;
}

// Integrations use userId for ownership
match /integrations/{integrationId} {
  allow read: if request.auth.uid == resource.data.userId;
  allow create: if request.auth != null;
  allow update: if request.auth.uid == resource.data.userId;
}
```

## API Endpoints

### Authentication Endpoints

- `POST /api/auth/set` - Set user session on server
- `GET /api/auth/get` - Get current user session
- `POST /api/auth/clear` - Clear user session

### Data API Endpoints

All data operations go through a generic API:

- `POST /api/data/read` - Read documents
- `POST /api/data/write` - Create documents
- `POST /api/data/update` - Update documents
- `POST /api/data/delete` - Soft delete documents

These endpoints automatically inject `userId` and `workspaceId` from the session.

## Common Patterns

### Creating a Document with User Context

```typescript
// Frontend
const integrationsApi = useIntegrationsApi()
await integrationsApi.create({
  provider: 'openai',
  name: 'OpenAI',
  // userId and workspaceId are added server-side
})

// Server-side (write.post.ts)
const newData = {
  workspaceId: session.currentWorkspace?.id,
  userId: session.user?.id,
  ...data,
  createdAt: serverTimestamp(),
  updatedAt: serverTimestamp(),
}
```

### Checking Ownership

```typescript
// In Firestore rules
allow update: if request.auth.uid == resource.data.userId;

// In server code
if (doc.data.userId !== session.user?.id) {
  throw createError({ statusCode: 403 })
}
```

## Migration Notes

The system was originally using `ownerId` in some places but has been standardized to use `userId` everywhere. A migration script is available at `/layers/auth-module/scripts/migrate-ownerid-to-userid.ts` for existing data.

## Best Practices

1. **Always use `userId`** for user identification in documents
2. **Let the server inject user context** - don't send userId from frontend
3. **Use workspace context** - most operations should be scoped to current workspace
4. **Check permissions** - use Firestore rules and server-side checks
5. **Handle session errors gracefully** - session setting is non-critical

## Troubleshooting

### Session Not Being Set

1. Check that all required fields are present in state before calling `setSessionServer()`
2. Verify that the user has a current workspace and profile
3. Check browser console for detailed logging
4. Ensure cookies are enabled and not blocked

### Permission Denied Errors

1. Verify the document has the correct `userId` field
2. Check that the user is authenticated
3. Ensure Firestore rules match the data structure
4. Use Firebase emulator UI to test rules

### Token Errors

1. Token structure: `session.user.token.idToken`
2. Fallback: `session.user.token.accessToken`
3. Ensure token is passed to Firebase server initialization
