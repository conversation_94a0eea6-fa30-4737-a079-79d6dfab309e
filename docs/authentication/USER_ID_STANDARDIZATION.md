# User ID Field Standardization

## Overview

This document outlines the standardization of user identification fields across the entire codebase, changing from the inconsistent use of `ownerId` and `userId` to using only `userId` throughout.

## Motivation

The codebase had inconsistent naming for user identification:
- **Workspaces** used `ownerId` to identify the workspace owner
- **Profiles, Members, and Integrations** used `userId`
- **Server endpoints** wrote both fields for backward compatibility

This inconsistency led to:
1. Confusion when developing new features
2. Potential bugs due to field name mismatches
3. More complex security rules
4. Redundant data storage

## Changes Made

### 1. Type Definitions
- **File**: `/layers/auth-module/types/workspace.ts`
- **Change**: `ownerId: string` → `userId: string` in Workspace interface

### 2. Firestore Security Rules
- **Files**:
  - `/firestore.rules`
  - `/layers/auth-module/firebase/firestore.rules`
  - `/layers/auth-module/deployment/firestore.rules`
- **Changes**: All workspace ownership checks now use `userId` instead of `ownerId`

### 3. Auth Composable
- **File**: `/layers/auth-module/composables/auth.ts`
- **Changes**: All workspace creation and data handling now uses `userId` field

### 4. Server API
- **File**: `/layers/auth-module/server/api/data/write.post.ts`
- **Change**: Removed backward compatibility code that wrote both `userId` and `ownerId`

### 5. Utility Functions
- **File**: `/layers/auth-module/utils/auth-helpers.ts`
- **Change**: Default workspace creation now uses `userId`

### 6. Test Files
- **Files**:
  - `/layers/auth-module/tests/unit/composables/useWorkspace.test.ts`
  - `/layers/auth-module/tests/integration/setup.ts`
  - `/layers/auth-module/tests/performance/benchmarks.ts`
- **Changes**: All test data now uses `userId`

### 7. Examples
- **File**: `/layers/auth-module/examples/workspace-management.example.vue`
- **Change**: Template references updated from `ownerId` to `userId`

### 8. Cloud Functions
- **Files**:
  - `/layers/auth-module/deployment/functions/src/triggers/onUserCreated.ts`
  - `/layers/auth-module/deployment/functions/src/triggers/onUserDeleted.ts`
  - `/layers/auth-module/deployment/functions/src/callable/removeUserFromWorkspace.ts`
- **Changes**: All workspace operations now use `userId`

## Migration

A migration script has been created to update existing Firestore documents:
- **Location**: `/layers/auth-module/scripts/migrate-ownerid-to-userid.ts`
- **Purpose**: Renames `ownerId` to `userId` in all workspace documents
- **Usage**: Set up Firebase Admin SDK credentials and run the script

## Benefits

1. **Consistency**: Single field name across all collections
2. **Simplicity**: Easier to understand and maintain
3. **Type Safety**: Reduces TypeScript errors from field mismatches
4. **Performance**: Slightly smaller document size (one less field)

## Developer Guidelines

Going forward:
1. Always use `userId` for user identification in any collection
2. Never introduce `ownerId` or similar variations
3. When checking workspace ownership, use `workspace.userId`
4. Keep field naming consistent with TypeScript conventions (camelCase)

## Rollback Plan

If needed, the changes can be rolled back by:
1. Reverting all code changes
2. Running a reverse migration script (change `userId` back to `ownerId` in workspaces)
3. Redeploying security rules

However, rollback is not recommended as the new structure is cleaner and more maintainable.
