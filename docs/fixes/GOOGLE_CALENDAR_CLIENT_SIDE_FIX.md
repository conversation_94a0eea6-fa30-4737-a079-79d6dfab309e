# Google Calendar Client-Side Environment Variable Fix

## 🚨 Problem
Google Calendar OAuth was failing with "Access blocked: Authorization Error - Missing required parameter: client_id" because the composable was trying to access server-side environment variables from the client-side.

## 🔍 Root Cause Analysis

**Client-Side Environment Variable Access Issue:**

In Nuxt.js, environment variables without the `NUXT_PUBLIC_` prefix are **NOT available on the client-side**. The Google Calendar composable was running in the browser and trying to access:

```typescript
// ❌ This doesn't work on client-side
const GOOGLE_CALENDAR_CONFIG: GoogleCalendarConfig = {
  clientId: process.env.GOOGLE_CLIENT_ID || '', // undefined on client
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || '', // undefined on client
  redirectUri: `${process.env.NUXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/integrations/google-calendar/callback`,
}
```

**Why This Happens:**
- Composables run in the browser (client-side)
- `process.env.GOOGLE_CLIENT_ID` is only available on the server
- Only `NUXT_PUBLIC_*` environment variables are exposed to the client
- The OAuth URL was being built with an empty `client_id` parameter

## ✅ Solution Applied

**1. Added OAuth Configuration to Runtime Config:**

Updated `layers/auth-module/nuxt.config.ts` to expose Google OAuth config to the client:

```typescript
runtimeConfig: {
  public: {
    oauth: {
      google: {
        clientId: process.env.GOOGLE_CLIENT_ID || '',
        baseUrl: process.env.NUXT_PUBLIC_BASE_URL || 'http://localhost:3000',
      },
    },
    // ... existing config
  },
},
```

**2. Updated Google Calendar Composable:**

Changed from direct `process.env` access to `useRuntimeConfig()`:

### Before (Broken):
```typescript
// ❌ Client-side can't access process.env.GOOGLE_CLIENT_ID
const GOOGLE_CALENDAR_CONFIG: GoogleCalendarConfig = {
  clientId: process.env.GOOGLE_CLIENT_ID || '',
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  redirectUri: `${process.env.NUXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/integrations/google-calendar/callback`,
  scope: [
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/calendar.events',
  ],
}
```

### After (Fixed):
```typescript
// ✅ Uses Nuxt runtime config for client-side access
function getGoogleCalendarConfig(): GoogleCalendarConfig {
  const config = useRuntimeConfig()

  return {
    clientId: config.public.oauth.google.clientId || '',
    clientSecret: '', // Not available on client-side (and not needed)
    redirectUri: `${config.public.oauth.google.baseUrl}/api/integrations/google-calendar/callback`,
    scope: [
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/calendar.events',
    ],
  }
}
```

**3. Updated OAuth URL Building:**

```typescript
// ✅ Now uses runtime config
const config = getGoogleCalendarConfig()

const params = new URLSearchParams({
  client_id: config.clientId, // Now properly populated
  redirect_uri: config.redirectUri,
  scope: config.scope.join(' '),
  // ... other params
})
```

## 📁 Files Modified

1. **`layers/auth-module/nuxt.config.ts`**
   - Added `oauth.google` configuration to `runtimeConfig.public`

2. **`layers/auth-module/composables/google-calendar.ts`**
   - Replaced static config with `getGoogleCalendarConfig()` function
   - Uses `useRuntimeConfig()` for client-side environment variable access
   - Updated OAuth URL building to use the new config function

## 🔧 How Nuxt Runtime Config Works

**Server-Side (API routes, server functions):**
```typescript
const config = useRuntimeConfig()
// Can access: config.secretKey (private)
// Can access: config.public.oauth.google.clientId (public)
```

**Client-Side (composables, components):**
```typescript
const config = useRuntimeConfig()
// Cannot access: config.secretKey (private)
// Can access: config.public.oauth.google.clientId (public)
```

## 🧪 Testing Steps

1. Ensure your `.env` file has `GOOGLE_CLIENT_ID` set
2. Restart the development server (to pick up nuxt.config.ts changes)
3. Try connecting Google Calendar again
4. The OAuth request should now include the correct `client_id` parameter
5. Google Calendar connection should complete successfully

## 🎯 Result

Google Calendar OAuth now properly accesses the Google Client ID on the client-side through Nuxt's runtime configuration system, eliminating the "missing client_id" error.

## 📚 Key Learnings

1. **Environment Variables in Nuxt:**
   - Server-side: Can access any `process.env.*`
   - Client-side: Only `NUXT_PUBLIC_*` or `runtimeConfig.public.*`

2. **OAuth Client ID Security:**
   - Client ID is **not sensitive** (it's visible in browser anyway)
   - Client Secret **must stay server-side only**
   - Runtime config is the proper way to expose client ID to browser

3. **Composable Environment Access:**
   - Always use `useRuntimeConfig()` in composables
   - Never use `process.env.*` directly in client-side code
   - Configure runtime config in `nuxt.config.ts` for proper exposure
