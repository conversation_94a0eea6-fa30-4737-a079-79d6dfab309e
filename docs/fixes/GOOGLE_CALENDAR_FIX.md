# Google Calendar OAuth Authentication Fix

## 🚨 Problem
Google Calendar integration was failing with "user not authenticated" error immediately when trying to connect.

## 🔍 Root Cause
The Google Calendar composable was checking for incorrect property names in the currentProfile object:

### Before (Incorrect Property Names):
```typescript
// Checking for wrong property name
if (!currentProfile.value?.userId || !currentProfile.value?.workspace_id) {
  throw new Error('User not authenticated')
}

// Using wrong property name in state
state: JSON.stringify({
  userId: currentProfile.value.userId,
  workspaceId: currentProfile.value.workspace_id,  // ❌ Wrong property name
  returnUrl: window.location.href,
}),

// Using wrong property name in API call
if (!currentProfile.value?.workspace_id) return

const response = await $fetch('/api/integrations/calendar', {
  params: {
    workspaceId: currentProfile.value.workspace_id,  // ❌ Wrong property name
  },
})
```

### After (Correct Property Names):
```typescript
// Correct property name
if (!currentProfile.value?.userId || !currentProfile.value?.workspaceId) {
  throw new Error('User not authenticated')
}

// Using correct property name in state
state: JSON.stringify({
  userId: currentProfile.value.userId,
  workspaceId: currentProfile.value.workspaceId,  // ✅ Correct property name
  returnUrl: window.location.href,
}),

// Using correct property name in API call
if (!currentProfile.value?.workspaceId) return

const response = await $fetch('/api/integrations/calendar', {
  params: {
    workspaceId: currentProfile.value.workspaceId,  // ✅ Correct property name
  },
})
```

## ✅ Solution Applied

Fixed all instances of `workspace_id` to `workspaceId` in the Google Calendar composable:

1. **Authentication check** (line 37)
2. **OAuth state creation** (line 55)
3. **Integration loading check** (line 145)
4. **API call parameter** (line 152)

## 📁 Files Modified

- **`layers/auth-module/composables/google-calendar.ts`**: Fixed property name inconsistencies

## 🔧 Key Differences from Gmail

Unlike Gmail integration, Google Calendar:
- ✅ **Does NOT need userinfo scopes** (doesn't fetch user profile)
- ✅ **Uses Google APIs client library directly** (not OAuthService)
- ✅ **Only needs calendar-specific scopes**:
  - `https://www.googleapis.com/auth/calendar`
  - `https://www.googleapis.com/auth/calendar.events`

## 🧪 Testing

After this fix:
1. The authentication check should pass
2. OAuth state should be created correctly
3. Google Calendar connection should work properly
4. Integration loading should function correctly

## 📋 Profile Object Structure

The currentProfile object from useAuth() has these properties:
- ✅ `userId` (string)
- ✅ `workspaceId` (string)
- ❌ NOT `workspace_id` (this was the bug)

This fix ensures the Google Calendar integration uses the correct property names that match the actual Profile interface structure.
