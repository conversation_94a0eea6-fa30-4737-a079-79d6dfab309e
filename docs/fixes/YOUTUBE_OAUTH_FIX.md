# YouTube OAuth 401 Error Fix

## 🚨 Problem
YouTube OAuth integration was failing with a 401 error:
```
Request is missing required authentication credential. Expected OAuth 2 access token, login cookie or other valid authentication credential.
```

## 🔍 Root Cause Analysis

**Same Issue as Gmail (Before Fix):**
The YouTube OAuth configuration was missing the necessary scopes to access user profile information. The system was trying to fetch user profile data from `https://www.googleapis.com/oauth2/v2/userinfo` but the OAuth token only had YouTube-specific permissions.

**YouTube OAuth Callback Analysis:**
```typescript
// layers/auth-module/server/api/integrations/youtube/oauth/callback.get.ts
// Line 37: Calls OAuthService.getUserProfile() which requires userinfo scopes
const profile = await OAuthService.getUserProfile('youtube', tokenData.accessToken)
```

## ✅ Solution Applied

**Added Missing Google OAuth Scopes:**

### Before (Missing Scopes):
```typescript
scopes: [
  'https://www.googleapis.com/auth/youtube',
  'https://www.googleapis.com/auth/youtube.upload',
]
```

### After (Complete Scopes):
```typescript
scopes: [
  'https://www.googleapis.com/auth/userinfo.profile', // ✅ Added
  'https://www.googleapis.com/auth/userinfo.email', // ✅ Added
  'https://www.googleapis.com/auth/youtube',
  'https://www.googleapis.com/auth/youtube.upload',
]
```

## 📁 Files Modified

1. **`layers/auth-module/config/social-providers.ts`**
   - Added `userinfo.profile` and `userinfo.email` scopes to YouTube configuration

2. **`docs/integrations/OAUTH_SETUP_GUIDE.md`**
   - Updated YouTube required scopes documentation

3. **`docs/integrations/INTEGRATION_IMPLEMENTATION_SUMMARY.md`**
   - Updated Google OAuth setup instructions to include YouTube scopes and API

## 🔧 Google Cloud Console Update Required

**IMPORTANT**: You need to update your Google Cloud Console OAuth consent screen to include the YouTube scopes:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **APIs & Services > OAuth consent screen**
3. Click **Edit App**
4. In the **Scopes** section, ensure these scopes are added:
   - `https://www.googleapis.com/auth/userinfo.profile`
   - `https://www.googleapis.com/auth/userinfo.email`
   - `https://www.googleapis.com/auth/youtube`
   - `https://www.googleapis.com/auth/youtube.upload`
5. Save the changes

## 📋 Complete Google Services OAuth Configuration

**All Google services now have consistent userinfo scopes:**

| Service | Userinfo Scopes | Service-Specific Scopes |
|---------|----------------|------------------------|
| Gmail | ✅ profile, email | gmail.readonly, gmail.send, gmail.modify |
| Google Calendar | ❌ Not needed* | calendar, calendar.events |
| YouTube | ✅ profile, email | youtube, youtube.upload |

*Google Calendar doesn't use OAuthService.getUserProfile(), so it doesn't need userinfo scopes.

## 🧪 Testing Steps

After updating the scopes in Google Cloud Console:
1. Clear any existing YouTube OAuth sessions
2. Try connecting YouTube integration again
3. The OAuth flow should now complete successfully
4. User profile information should be fetched without errors

## 🔒 Security & Scope Explanation

**Added Scopes:**
- **`userinfo.profile`**: Access to basic profile information (name, picture)
- **`userinfo.email`**: Access to user's email address

**Existing YouTube Scopes:**
- **`youtube`**: Manage YouTube account and videos
- **`youtube.upload`**: Upload videos to YouTube

## 📊 OAuth Flow Consistency

**Now all Google integrations that use OAuthService.getUserProfile() have the required userinfo scopes:**
- ✅ **Gmail**: Has userinfo scopes (fixed previously)
- ✅ **YouTube**: Has userinfo scopes (fixed now)
- ✅ **Google Calendar**: Doesn't need userinfo scopes (uses different flow)

This ensures consistent OAuth behavior across all Google services that require user profile information.
