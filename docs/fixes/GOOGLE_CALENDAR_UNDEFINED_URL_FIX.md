# Google Calendar OAuth "undefined" URL Fix

## 🐛 Problem

Users encountered a "Page not found" error when trying to connect Google Calendar integration. The error URL showed:
```
/api/integrations/google-calendar/undefined/user/integrations?error=invalid_request
```

This indicated that an `undefined` parameter was being inserted into the OAuth callback URL construction.

## 🔍 Root Cause Analysis

The issue was in the OAuth callback handlers that were using `process.env.NUXT_PUBLIC_BASE_URL` directly instead of the runtime configuration. When this environment variable wasn't set, it resulted in `undefined` being used in URL construction.

**Affected Files:**
- `layers/auth-module/server/api/integrations/google-calendar/callback.get.ts`
- `layers/auth-module/server/api/integrations/outlook-calendar/oauth/callback.get.ts`

**Problem Code:**
```typescript
// ❌ This causes undefined URLs when env var is not set
const redirectUrl = returnUrl || `${process.env.NUXT_PUBLIC_BASE_URL}/user/integrations`
const errorUrl = `${process.env.NUXT_PUBLIC_BASE_URL}/user/integrations?error=${error}`
```

## ✅ Solution Applied

**1. Updated Google Calendar Callback Handler:**

Fixed `layers/auth-module/server/api/integrations/google-calendar/callback.get.ts`:

```typescript
export default defineEventHandler(async (event) => {
  try {
    const config = useRuntimeConfig()
    const baseUrl = config.public.oauth?.google?.baseUrl || 'http://localhost:3000'

    // ... OAuth processing ...

    // Exchange code for tokens
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${baseUrl}/api/integrations/google-calendar/callback`,
    )

    // ... token processing ...

    // Redirect back to the original page
    const redirectUrl = returnUrl || `${baseUrl}/user/integrations`
    return sendRedirect(event, `${redirectUrl}?connected=google-calendar`)
  }
  catch (error) {
    console.error('Google Calendar OAuth callback error:', error)

    const config = useRuntimeConfig()
    const baseUrl = config.public.oauth?.google?.baseUrl || 'http://localhost:3000'
    const errorUrl = `${baseUrl}/user/integrations?error=${encodeURIComponent(error.message)}`
    return sendRedirect(event, errorUrl)
  }
})
```

**2. Updated Outlook Calendar Callback Handler:**

Fixed `layers/auth-module/server/api/integrations/outlook-calendar/oauth/callback.get.ts` with the same pattern.

## 🔧 Technical Details

**Why This Happened:**
- Server-side callback handlers were using `process.env.NUXT_PUBLIC_BASE_URL` directly
- This environment variable was undefined in the development environment
- URL construction resulted in `undefined/user/integrations` paths
- The runtime config provides a fallback to `http://localhost:3000` when the env var is not set

**The Fix:**
- Use `useRuntimeConfig()` to access the base URL with proper fallback
- Access `config.public.oauth.google.baseUrl` which has a default value
- Ensures consistent URL construction across development and production

## 🧪 Testing

1. **Start Development Server:**
   ```bash
   cd apps/pib && npm run dev
   ```

2. **Test Google Calendar Integration:**
   - Navigate to `/user/integrations`
   - Click "Connect Google Calendar"
   - Verify OAuth flow completes without "undefined" URLs
   - Check that success/error redirects work properly

## 🎯 Result

- ✅ Google Calendar OAuth flow now works correctly
- ✅ No more "undefined" in callback URLs
- ✅ Proper fallback to localhost:3000 in development
- ✅ Consistent URL handling across all OAuth integrations
- ✅ Both success and error redirects work properly

## 📝 Environment Variables

Make sure to set the base URL in production:

```bash
# Production
NUXT_PUBLIC_BASE_URL=https://yourdomain.com

# Development (optional - defaults to http://localhost:3000)
NUXT_PUBLIC_BASE_URL=http://localhost:3000
```

The runtime config in `layers/auth-module/nuxt.config.ts` provides the fallback:

```typescript
runtimeConfig: {
  public: {
    oauth: {
      google: {
        clientId: process.env.GOOGLE_CLIENT_ID || '',
        baseUrl: process.env.NUXT_PUBLIC_BASE_URL || 'http://localhost:3000',
      },
    },
  },
}
```
