# Documentation Directory

This directory contains all project documentation organized by category for better discoverability and maintenance.

## 📁 Directory Structure

### `/authentication/`
Authentication-related documentation:
- `AUTH_FLOW_DOCUMENTATION.md` - Authentication flow and user journey documentation
- `USER_ID_STANDARDIZATION.md` - User ID field naming conventions and migration

### `/database/`
Database-related documentation:
- `DATABASE_FIELD_NAMING_CHANGES.md` - Database schema changes and field naming conventions

### `/epics/`
High-level epic documentation:
- `epic-1-profile-state-migration.md` - Profile state migration epic
- `epic-2-data-api-integration.md` - Data API integration epic
- `epic-3-module-extensibility.md` - Module extensibility epic

### `/fixes/`
Troubleshooting and fix documentation:
- `GMAIL_OAUTH_FIX.md` - Gmail OAuth 401 error resolution
- `GOOGLE_CALENDAR_*.md` - Google Calendar integration fixes
- `YOUTUBE_OAUTH_FIX.md` - YouTube OAuth 401 error resolution

### `/integrations/`
Integration setup and implementation:
- `EMAIL_SYSTEM_IMPLEMENTATION.md` - Email system implementation details
- `GMAIL_CONSENT_IMPLEMENTATION.md` - Gmail consent screen setup
- `INTEGRATION_IMPLEMENTATION_SUMMARY.md` - Complete integration overview
- `OAUTH_SETUP_GUIDE.md` - OAuth setup guide for all providers

### `/memories/`
Implementation memories and historical documentation:
- `claude-integration-implementation.md` - Claude AI integration implementation notes

### `/modules/`
Module-specific documentation:
- `auth-module/` - Authentication module documentation
- `writer-module/` - Writer module documentation

### `/stories/`
User story documentation:
- Story definitions and acceptance criteria for specific features

### `/testing/`
Testing strategies and plans:
- `calendar-testing-strategy.md` - Calendar integration testing approach

## 📋 Top-Level Documentation

### Core Documentation
- `MODULE_ARCHITECTURE.md` - Complete module system architecture guide
- `FIRESTORE_SECURITY_RULES_TEMPLATE.md` - Firestore security rules template
- `operational-guidelines.md` - Operational guidelines and best practices
- `mcp-capabilities.md` - MCP (Model Context Protocol) capabilities

### Calendar System
- `calendar-api-reference.md` - Calendar API reference
- `calendar-quick-start.md` - Quick start guide for calendar features
- `calendar-sidebar-implementation.md` - Calendar sidebar implementation
- `calendar-system-documentation.md` - Complete calendar system documentation

### Migration & Planning
- `PROFILE_MIGRATION_PLAN.md` - Profile migration strategy and implementation plan

## 🔍 Finding Documentation

### By Feature Area:
- **Authentication**: See `/authentication/` and `/integrations/`
- **Calendar**: See top-level calendar files and `/testing/`
- **Database**: See `/database/`
- **Modules**: See `/modules/` and `MODULE_ARCHITECTURE.md`
- **Troubleshooting**: See `/fixes/`

### By Development Phase:
- **Planning**: See `/epics/` and `/stories/`
- **Implementation**: See `/modules/` and `/integrations/`
- **Testing**: See `/testing/`
- **Maintenance**: See `/fixes/` and `/memories/`

## 📖 Getting Started

1. **New to the project?** Start with `MODULE_ARCHITECTURE.md`
2. **Setting up integrations?** Check `/integrations/`
3. **Having issues?** Look in `/fixes/`
4. **Working on a specific module?** Check `/modules/[module-name]/`

## 🔄 Documentation Updates

When adding new documentation:
1. Place files in the appropriate category directory
2. Update this README if adding new categories
3. Use descriptive filenames that indicate the content type
4. Keep the directory structure flat within categories (avoid deep nesting)

## 📝 Naming Conventions

- **Fixes**: `[FEATURE]_[ISSUE]_FIX.md`
- **Implementation**: `[FEATURE]_IMPLEMENTATION.md`
- **Guides**: `[FEATURE]_GUIDE.md` or `[FEATURE]_SETUP.md`
- **Architecture**: `[MODULE]_ARCHITECTURE.md`
- **Stories**: `[ID]-[title].story.md`
- **Epics**: `epic-[ID]-[title].md`
