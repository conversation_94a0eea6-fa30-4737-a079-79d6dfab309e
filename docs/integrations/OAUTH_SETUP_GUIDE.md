# OAuth Integration Setup Guide

This guide provides step-by-step instructions for setting up OAuth integrations for all supported platforms in your application.

## Required Environment Variables

Add these environment variables to your `.env` file:

```bash
# Google Services (Gmail + Google Calendar + YouTube)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Microsoft Services (Outlook + Outlook Calendar)
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret

# Social Media Platforms
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
INSTAGRAM_CLIENT_ID=your-instagram-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
TWITTER_CLIENT_ID=your-twitter-client-id
TWITTER_CLIENT_SECRET=your-twitter-client-secret
REDDIT_CLIENT_ID=your-reddit-client-id
REDDIT_CLIENT_SECRET=your-reddit-client-secret
PINTEREST_CLIENT_ID=your-pinterest-client-id
PINTEREST_CLIENT_SECRET=your-pinterest-client-secret
TIKTOK_CLIENT_ID=your-tiktok-client-id
TIKTOK_CLIENT_SECRET=your-tiktok-client-secret
YOUTUBE_CLIENT_ID=your-youtube-client-id
YOUTUBE_CLIENT_SECRET=your-youtube-client-secret
```

**Important:** After updating environment variables, restart your development server to ensure the runtime configuration picks up the new values.

## Platform Setup Instructions

### 1. Google (Gmail + Google Calendar)

**Setup Steps:**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the following APIs:
   - Gmail API
   - Google Calendar API
   - Google+ API (for profile info)
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Configure OAuth consent screen
6. Add authorized redirect URIs:
   - `https://yourdomain.com/api/integrations/gmail/oauth/callback`
   - `https://yourdomain.com/api/integrations/google-calendar/callback`

**Required Scopes:**
- `https://www.googleapis.com/auth/userinfo.profile`
- `https://www.googleapis.com/auth/userinfo.email`
- `https://www.googleapis.com/auth/gmail.readonly`
- `https://www.googleapis.com/auth/gmail.send`
- `https://www.googleapis.com/auth/gmail.modify`
- `https://www.googleapis.com/auth/calendar`
- `https://www.googleapis.com/auth/calendar.events`

### 2. Microsoft (Outlook + Outlook Calendar)

**Setup Steps:**
1. Go to [Azure Portal](https://portal.azure.com/)
2. Navigate to "Azure Active Directory" → "App registrations"
3. Click "New registration"
4. Set redirect URI: `https://yourdomain.com/api/integrations/outlook/oauth/callback`
5. Go to "API permissions" and add:
   - Microsoft Graph → Delegated permissions
   - Mail.ReadWrite, Mail.Send, Calendars.ReadWrite
6. Go to "Certificates & secrets" → "New client secret"

**Required Permissions:**
- `https://graph.microsoft.com/Mail.ReadWrite`
- `https://graph.microsoft.com/Mail.Send`
- `https://graph.microsoft.com/Calendars.ReadWrite`

### 3. Facebook

**Setup Steps:**
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add "Facebook Login" product
4. Set Valid OAuth Redirect URIs: `https://yourdomain.com/api/integrations/facebook/oauth/callback`
5. Add required permissions in App Review

**Required Permissions:**
- `pages_manage_posts`
- `pages_read_engagement`
- `pages_read_user_content`

### 4. Instagram

**Setup Steps:**
1. Use the same Facebook app from above
2. Add "Instagram Basic Display" product
3. Set redirect URI: `https://yourdomain.com/api/integrations/instagram/oauth/callback`

**Required Permissions:**
- `user_profile`
- `user_media`

### 5. LinkedIn

**Setup Steps:**
1. Go to [LinkedIn Developers](https://www.linkedin.com/developers/)
2. Create a new app
3. Add "Sign In with LinkedIn" product
4. Set redirect URL: `https://yourdomain.com/api/integrations/linkedin/oauth/callback`

**Required Permissions:**
- `r_liteprofile`
- `r_emailaddress`
- `w_member_social`

### 6. X (Twitter)

**Setup Steps:**
1. Go to [Twitter Developer Portal](https://developer.twitter.com/)
2. Create a new app
3. Enable OAuth 2.0
4. Set callback URL: `https://yourdomain.com/api/integrations/twitter/oauth/callback`

**Required Scopes:**
- `tweet.read`
- `tweet.write`
- `users.read`

### 7. Reddit

**Setup Steps:**
1. Go to [Reddit App Preferences](https://www.reddit.com/prefs/apps)
2. Create a new app (type: web app)
3. Set redirect URI: `https://yourdomain.com/api/integrations/reddit/oauth/callback`

**Required Scopes:**
- `identity`
- `read`
- `submit`

### 8. Pinterest

**Setup Steps:**
1. Go to [Pinterest Developers](https://developers.pinterest.com/)
2. Create a new app
3. Set redirect URI: `https://yourdomain.com/api/integrations/pinterest/oauth/callback`

**Required Scopes:**
- `user_accounts:read`
- `pins:read`
- `pins:write`

### 9. TikTok

**Setup Steps:**
1. Go to [TikTok Developers](https://developers.tiktok.com/)
2. Create a new app
3. Set redirect URI: `https://yourdomain.com/api/integrations/tiktok/oauth/callback`

**Required Scopes:**
- `user.info.basic`
- `video.list`

### 10. YouTube

**Setup Steps:**
1. Use the same Google Cloud project from step 1
2. Enable YouTube Data API v3
3. Use the same OAuth credentials
4. Set redirect URI: `https://yourdomain.com/api/integrations/youtube/oauth/callback`

**Required Scopes:**
- `https://www.googleapis.com/auth/userinfo.profile`
- `https://www.googleapis.com/auth/userinfo.email`
- `https://www.googleapis.com/auth/youtube`
- `https://www.googleapis.com/auth/youtube.upload`

## Testing Your Setup

1. Start your application
2. Navigate to `/user/integrations`
3. Try connecting each integration
4. Check that OAuth flows complete successfully
5. Verify integrations are saved to your database

## Troubleshooting

### Common Issues:

1. **"Invalid redirect URI"**
   - Ensure redirect URIs match exactly in your OAuth app settings
   - Include both HTTP (development) and HTTPS (production) URLs

2. **"Invalid client credentials"**
   - Double-check your client ID and secret in environment variables
   - Ensure no extra spaces or characters

3. **"Insufficient permissions"**
   - Review required scopes for each platform
   - Some platforms require app review for certain permissions

4. **"OAuth state mismatch"**
   - This is a security feature - ensure cookies are working properly
   - Check that your domain supports secure cookies in production

### Development vs Production:

- **Development**: Use `http://localhost:3000` for redirect URIs
- **Production**: Use your actual domain with HTTPS

## Security Notes

- All OAuth tokens are encrypted before storage
- State parameters prevent CSRF attacks
- Refresh tokens are used to maintain long-term access
- Tokens are automatically refreshed when expired

## Support

If you encounter issues:
1. Check the browser console for errors
2. Verify environment variables are set correctly
3. Ensure OAuth apps are configured with correct redirect URIs
4. Test with a simple OAuth flow first before complex integrations
