# Calendar Feature Worktree Activation Guide

## Worktree Details
- **Feature Name**: Calendar
- **Branch**: feature/calendar-20250705
- **Location**: ../bmad-calendar
- **Base Branch**: main
- **Created**: 2025-07-05 07:21:00

## Next Steps

### 1. Open New Claude Code Instance
```bash
cd ../bmad-calendar
claude-code
```

### 2. Verify Environment
Run these commands in the new worktree to ensure everything is set up correctly:
```bash
git status                    # Should show feature/calendar-20250705 branch
pnpm install                  # Install dependencies
ls .claude/state/worktrees/   # Should show calendar directory
```

### 3. Start Development with BMAD
The full BMAD framework is available with isolated state:

#### Planning Phase
```
/pm-orchestrate
```
Create Product Requirements Document for calendar feature

#### Architecture Phase
```
/architect-design
```
Design the calendar module architecture

#### Development Phase
```
/dev-command
```
Implement calendar components and functionality

### 4. Recommended Development Flow
1. **Requirements Gathering**: Use `/pm-orchestrate` to define user stories and acceptance criteria
2. **Architecture Design**: Use `/architect-design` to plan module structure and data models
3. **Implementation**: Use `/dev-command` for step-by-step development
4. **Quality Assurance**: Regular testing and code reviews
5. **Integration**: When ready, use `/feature-merge` to integrate back to main

### 5. Isolated Features Available
- ✅ Independent Git branch and history
- ✅ Isolated BMAD state and context evolution
- ✅ Separate MCP conversation tracking
- ✅ Independent tool selection and caching
- ✅ Feature-specific context engineering

### 6. Development Guidelines
- Follow existing module patterns in `/layers/`
- Create new module as `/layers/calendar-module/`
- Use Vue 3 Composition API and TypeScript
- Integrate with Firebase for data persistence
- Leverage Tairo UI components for consistency
- Implement proper test coverage

### 7. When Feature is Complete
```
/feature-merge
```
This will handle the integration back to the main repository.

## Safety Notes
- All development is isolated from main repository
- No risk of interfering with other parallel features
- State and context are completely separate
- Can run multiple features simultaneously

---
**Ready to start? Open a new terminal and run:**
```bash
cd ../bmad-calendar && claude-code
```
