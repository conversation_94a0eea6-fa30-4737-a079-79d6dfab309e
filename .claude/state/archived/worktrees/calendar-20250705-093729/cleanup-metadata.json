{"feature_name": "calendar", "cleanup_date": "2025-07-05T09:37:29Z", "worktree_directory": "/Users/<USER>/Projects/own/bmad-calendar", "branch_name": "feature/calendar-20250705", "final_commit": "f4685b5077e3e132f568ae827e4252492e2c95ae", "final_commit_message": "fix(toast): Replace invalid 'red' color with 'danger' in toast notifications", "cleanup_method": "manual_cleanup", "had_uncommitted_changes": false, "restoration_command": "git worktree add /Users/<USER>/Projects/own/bmad-calendar feature/calendar-20250705"}