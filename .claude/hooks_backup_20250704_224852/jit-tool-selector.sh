#!/bin/bash

# Just-in-Time Tool Selection Service
# Dynamically selects optimal MCP tools based on context, complexity, and agent type

set -euo pipefail

# Configuration
# Find project root and .claude directory
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

PROJECT_ROOT=$(find_project_root)
if [ -z "$PROJECT_ROOT" ]; then
    echo "ERROR: Could not find project root with .claude directory" >&2
    exit 1
fi

JIT_CONFIG_FILE="$PROJECT_ROOT/bmad-agent/config/enhanced-mcp-capabilities.yml"
TOOL_USAGE_LOG="$PROJECT_ROOT/.claude/state/context-engine/tool-usage.log"
SELECTION_CACHE="$PROJECT_ROOT/.claude/state/context-engine/tool-selection-cache.json"

# Ensure required tools are available
check_dependencies() {
    local missing_deps=()
    
    if ! command -v yq >/dev/null 2>&1; then
        missing_deps+=("yq")
    fi
    
    if ! command -v jq >/dev/null 2>&1; then
        missing_deps+=("jq")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        echo "ERROR: Missing dependencies: ${missing_deps[*]}" >&2
        echo "Please install missing tools to use JIT tool selection" >&2
        return 1
    fi
    
    return 0
}

# Calculate tool effectiveness score
calculate_tool_score() {
    local tool_name="$1"
    local context="$2"
    local complexity="$3"
    local agent_type="$4"
    
    local effectiveness=0.0
    local context_match=0.0
    local complexity_match=0.0
    local agent_preference=0.0
    
    # Get effectiveness from config
    if [[ -f "$JIT_CONFIG_FILE" ]]; then
        effectiveness=$(yq eval ".tool_effectiveness.*.tools.${tool_name}.effectiveness // 0.0" "$JIT_CONFIG_FILE" 2>/dev/null || echo "0.0")
        
        # Check context match
        local contexts
        contexts=$(yq eval ".tool_effectiveness.*.tools.${tool_name}.contexts[]?" "$JIT_CONFIG_FILE" 2>/dev/null || echo "")
        if echo "$contexts" | grep -q "$context"; then
            context_match=1.0
        fi
        
        # Check complexity suitability
        local complexities
        complexities=$(yq eval ".tool_effectiveness.*.tools.${tool_name}.complexity_suitability[]?" "$JIT_CONFIG_FILE" 2>/dev/null || echo "")
        if echo "$complexities" | grep -q "$complexity"; then
            complexity_match=1.0
        fi
        
        # Check agent preference
        local agent_tools
        agent_tools=$(yq eval ".agent_tool_preferences.${agent_type}.primary_tools[]? + .agent_tool_preferences.${agent_type}.secondary_tools[]?" "$JIT_CONFIG_FILE" 2>/dev/null || echo "")
        if echo "$agent_tools" | grep -q "$tool_name"; then
            agent_preference=1.0
        fi
    fi
    
    # Calculate weighted score (based on context_aware algorithm)
    local score
    score=$(echo "scale=3; ($effectiveness * 0.4) + ($context_match * 0.3) + ($complexity_match * 0.2) + ($agent_preference * 0.1)" | bc -l)
    
    echo "$score"
}

# Get tool recommendations based on context
get_context_tools() {
    local context="$1"
    local tool_type="${2:-recommended}"  # required, recommended, optional
    
    if [[ -f "$JIT_CONFIG_FILE" ]]; then
        yq eval ".context_tool_mapping.${context}.${tool_type}[]?" "$JIT_CONFIG_FILE" 2>/dev/null || echo ""
    fi
}

# Select optimal tools for given parameters
select_optimal_tools() {
    local agent_type="$1"
    local context="$2"
    local complexity="$3"
    local max_tools="${4:-5}"
    local algorithm="${5:-context_aware}"
    
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Log the selection request
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] JIT Selection: agent=$agent_type, context=$context, complexity=$complexity, max=$max_tools, algo=$algorithm" >> "$TOOL_USAGE_LOG"
    
    # Check cache first
    local cache_key="${agent_type}-${context}-${complexity}-${algorithm}"
    local cached_result=""
    
    if [[ -f "$SELECTION_CACHE" ]]; then
        cached_result=$(jq -r --arg key "$cache_key" '.[$key].tools // empty' "$SELECTION_CACHE" 2>/dev/null || echo "")
        local cache_time
        cache_time=$(jq -r --arg key "$cache_key" '.[$key].timestamp // empty' "$SELECTION_CACHE" 2>/dev/null || echo "")
        
        # Check if cache is still valid (1 hour)
        if [[ -n "$cached_result" && -n "$cache_time" ]]; then
            local cache_age
            cache_age=$(date -d "$cache_time" +%s 2>/dev/null || echo "0")
            local current_time
            current_time=$(date +%s)
            
            if [[ $((current_time - cache_age)) -lt 3600 ]]; then
                echo "# Cached JIT Tool Selection Results"
                echo "$cached_result"
                return 0
            fi
        fi
    fi
    
    # Get all available tools
    local all_tools=()
    
    # Start with context-required tools
    local required_tools
    required_tools=$(get_context_tools "$context" "required")
    
    # Add context-recommended tools
    local recommended_tools
    recommended_tools=$(get_context_tools "$context" "recommended")
    
    # Add agent-specific tools based on complexity
    local agent_tools=""
    if [[ -f "$JIT_CONFIG_FILE" ]]; then
        agent_tools=$(yq eval ".agent_tool_preferences.${agent_type}.complexity_mapping.${complexity}[]?" "$JIT_CONFIG_FILE" 2>/dev/null || echo "")
    fi
    
    # Combine all potential tools
    local potential_tools
    potential_tools=$(echo -e "$required_tools\n$recommended_tools\n$agent_tools" | sort -u | grep -v '^$' || echo "")
    
    # Score and rank tools
    declare -A tool_scores
    local tool_list=()
    
    while IFS= read -r tool; do
        if [[ -n "$tool" ]]; then
            local score
            score=$(calculate_tool_score "$tool" "$context" "$complexity" "$agent_type")
            tool_scores["$tool"]="$score"
            tool_list+=("$tool")
        fi
    done <<< "$potential_tools"
    
    # Sort tools by score (descending)
    local sorted_tools=()
    for tool in "${tool_list[@]}"; do
        sorted_tools+=("${tool_scores[$tool]}:$tool")
    done
    
    # Sort and extract top tools
    local selected_tools=()
    local count=0
    
    printf '%s\n' "${sorted_tools[@]}" | sort -rn | while IFS=':' read -r score tool; do
        if [[ $count -lt $max_tools ]]; then
            selected_tools+=("$tool")
            echo "# Tool: $tool (Score: $score)"
            ((count++))
        fi
    done
    
    # Cache the results
    if [[ ! -f "$SELECTION_CACHE" ]]; then
        echo "{}" > "$SELECTION_CACHE"
    fi
    
    local selected_tools_json
    selected_tools_json=$(printf '%s\n' "${selected_tools[@]}" | jq -R -s 'split("\n")[:-1]')
    
    jq --arg key "$cache_key" --arg ts "$timestamp" --argjson tools "$selected_tools_json" \
        '.[$key] = {"tools": $tools | join("\n"), "timestamp": $ts}' \
        "$SELECTION_CACHE" > "$SELECTION_CACHE.tmp" && mv "$SELECTION_CACHE.tmp" "$SELECTION_CACHE"
}

# Suggest MCP tools based on current context
suggest_mcp_tools() {
    local task_description="$1"
    local current_agent="${2:-james}"
    
    # Analyze task description to determine context and complexity
    local context="general"
    local complexity="medium"
    
    # Context detection based on keywords
    if echo "$task_description" | grep -qi "implement\|code\|develop\|build\|create"; then
        context="implementation"
    elif echo "$task_description" | grep -qi "debug\|fix\|error\|bug\|issue"; then
        context="debugging"
    elif echo "$task_description" | grep -qi "architecture\|design\|structure\|pattern"; then
        context="architecture"
    elif echo "$task_description" | grep -qi "research\|find\|search\|investigate"; then
        context="research"
    elif echo "$task_description" | grep -qi "test\|validate\|verify\|check"; then
        context="testing"
    elif echo "$task_description" | grep -qi "security\|secure\|vulnerability\|audit"; then
        context="security"
    elif echo "$task_description" | grep -qi "review\|quality\|standard\|improve"; then
        context="quality"
    fi
    
    # Complexity detection
    local word_count
    word_count=$(echo "$task_description" | wc -w)
    
    if [[ $word_count -lt 5 ]]; then
        complexity="simple"
    elif [[ $word_count -gt 15 ]] || echo "$task_description" | grep -qi "complex\|advanced\|system\|integration\|comprehensive"; then
        complexity="complex"
    fi
    
    if echo "$task_description" | grep -qi "expert\|full-stack\|end-to-end\|enterprise"; then
        complexity="expert"
    fi
    
    echo "# JIT MCP Tool Suggestions"
    echo "Context: $context"
    echo "Complexity: $complexity"
    echo "Agent: $current_agent"
    echo ""
    
    select_optimal_tools "$current_agent" "$context" "$complexity" 5 "context_aware"
}

# Record tool usage and effectiveness
record_tool_usage() {
    local tool_name="$1"
    local context="$2"
    local success="${3:-true}"
    local response_time="${4:-0}"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    local usage_stats_file="$PROJECT_ROOT/.claude/state/context-engine/tool-usage-stats.json"
    
    # Initialize file if it doesn't exist
    if [[ ! -f "$usage_stats_file" ]]; then
        echo '{"tool_usage": [], "last_updated": null}' > "$usage_stats_file"
    fi
    
    # Add usage record
    jq --arg tool "$tool_name" --arg ctx "$context" --arg success "$success" --arg time "$response_time" --arg ts "$timestamp" \
        '.tool_usage += [{
            "tool": $tool,
            "context": $ctx,
            "success": ($success | test("true")),
            "response_time": ($time | tonumber),
            "timestamp": $ts
        }] | .last_updated = $ts' \
        "$usage_stats_file" > "$usage_stats_file.tmp" && mv "$usage_stats_file.tmp" "$usage_stats_file"
    
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Recorded usage: $tool_name ($context) - Success: $success, Time: ${response_time}s" >> "$TOOL_USAGE_LOG"
}

# Get tool usage analytics
get_tool_analytics() {
    local tool_name="${1:-all}"
    local days="${2:-7}"
    
    local usage_stats_file="$PROJECT_ROOT/.claude/state/context-engine/tool-usage-stats.json"
    
    if [[ ! -f "$usage_stats_file" ]]; then
        echo "No usage statistics available"
        return 1
    fi
    
    echo "# Tool Usage Analytics (Last $days days)"
    echo ""
    
    if [[ "$tool_name" == "all" ]]; then
        # Overall statistics
        local total_usage
        total_usage=$(jq '.tool_usage | length' "$usage_stats_file")
        local success_rate
        success_rate=$(jq '.tool_usage | map(select(.success)) | length / (.tool_usage | length) * 100' "$usage_stats_file" 2>/dev/null || echo "0")
        
        echo "Total tool usage: $total_usage"
        echo "Overall success rate: ${success_rate}%"
        echo ""
        
        # Top tools by usage
        echo "## Top Tools by Usage"
        jq -r '.tool_usage | group_by(.tool) | map({tool: .[0].tool, count: length}) | sort_by(.count) | reverse | .[] | "\(.tool): \(.count)"' "$usage_stats_file" | head -10
        
    else
        # Specific tool statistics
        echo "## Statistics for $tool_name"
        local tool_usage
        tool_usage=$(jq --arg tool "$tool_name" '.tool_usage | map(select(.tool == $tool)) | length' "$usage_stats_file")
        local tool_success_rate
        tool_success_rate=$(jq --arg tool "$tool_name" '.tool_usage | map(select(.tool == $tool and .success)) | length / (.tool_usage | map(select(.tool == $tool)) | length) * 100' "$usage_stats_file" 2>/dev/null || echo "0")
        
        echo "Usage count: $tool_usage"
        echo "Success rate: ${tool_success_rate}%"
    fi
}

# Main function
main() {
    local action="${1:-help}"
    
    # Check dependencies first
    if ! check_dependencies; then
        echo "Falling back to basic tool selection" >&2
        return 1
    fi
    
    case "$action" in
        "select")
            if [[ $# -ge 4 ]]; then
                select_optimal_tools "$2" "$3" "$4" "${5:-5}" "${6:-context_aware}"
            else
                echo "Usage: $0 select <agent_type> <context> <complexity> [max_tools] [algorithm]"
                exit 1
            fi
            ;;
        "suggest")
            if [[ $# -ge 2 ]]; then
                suggest_mcp_tools "$2" "${3:-james}"
            else
                echo "Usage: $0 suggest <task_description> [agent_type]"
                exit 1
            fi
            ;;
        "record")
            if [[ $# -ge 3 ]]; then
                record_tool_usage "$2" "$3" "${4:-true}" "${5:-0}"
            else
                echo "Usage: $0 record <tool_name> <context> [success] [response_time]"
                exit 1
            fi
            ;;
        "analytics")
            get_tool_analytics "${2:-all}" "${3:-7}"
            ;;
        "help"|*)
            cat << EOF
Just-in-Time Tool Selection Service

Usage: $0 <action> [arguments...]

Actions:
  select <agent_type> <context> <complexity> [max_tools] [algorithm]
    Select optimal tools for given parameters
    
  suggest <task_description> [agent_type]
    Suggest MCP tools based on task description
    
  record <tool_name> <context> [success] [response_time]
    Record tool usage and effectiveness
    
  analytics [tool_name] [days]
    Get tool usage analytics
    
  help
    Show this help message

Examples:
  $0 select james implementation medium 3
  $0 suggest "implement user authentication system" james
  $0 record zen.analyze debugging true 2.5
  $0 analytics zen.codereview 30
EOF
            ;;
    esac
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi