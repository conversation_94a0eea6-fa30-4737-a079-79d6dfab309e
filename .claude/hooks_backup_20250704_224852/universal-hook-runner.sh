#!/bin/bash

# Universal Hook Runner - Works in ANY project, ANY directory
# This script dynamically finds the .claude directory and runs hooks
# No hardcoded paths - truly portable across all projects

# Set error handling
set -o pipefail
trap '' PIPE

# Function to find .claude directory from current location
find_project_claude_dir() {
    local search_dir="$PWD"
    
    # Walk up directory tree until we find .claude or reach root
    while [ "$search_dir" != "/" ]; do
        if [ -d "$search_dir/.claude" ]; then
            echo "$search_dir/.claude"
            return 0
        fi
        search_dir=$(dirname "$search_dir")
    done
    
    # Not found
    return 1
}

# Get the hook name from command line argument
HOOK_NAME="$1"
if [ -z "$HOOK_NAME" ]; then
    echo "Error: Hook name required" >&2
    exit 2
fi
shift  # Remove hook name from arguments

# Find the .claude directory for THIS project
CLAUDE_DIR=$(find_project_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "Error: Cannot find .claude directory from $(pwd)" >&2
    exit 2
fi

# Construct hook path
HOOK_SCRIPT="$CLAUDE_DIR/hooks/$HOOK_NAME"

# Verify hook exists and is executable
if [ ! -f "$HOOK_SCRIPT" ]; then
    echo "Error: Hook not found: $HOOK_SCRIPT" >&2
    exit 2
fi

if [ ! -x "$HOOK_SCRIPT" ]; then
    echo "Error: Hook not executable: $HOOK_SCRIPT" >&2
    exit 2
fi

# MCP Conversation Flow Validation
MCP_VALIDATOR="$CLAUDE_DIR/hooks/mcp-conversation-validator.sh"
if [ -x "$MCP_VALIDATOR" ]; then
    # Capture stdin for validation
    STDIN_DATA=$(cat)
    
    # Validate MCP conversation flow if this looks like MCP data
    if echo "$STDIN_DATA" | grep -q "tool_use\|tool_result\|mcp__"; then
        if ! echo "$STDIN_DATA" | "$MCP_VALIDATOR" validate; then
            echo "Warning: MCP conversation flow validation failed" >&2
            # Attempt recovery
            "$MCP_VALIDATOR" recover graceful_degradation >&2
        fi
    fi
    
    # Execute the hook with validated stdin
    echo "$STDIN_DATA" | "$HOOK_SCRIPT" "$@"
    hook_exit_code=$?
else
    # Fallback to direct execution if validator not available
    cat | "$HOOK_SCRIPT" "$@"
    hook_exit_code=$?
fi

# Optional: Debug output to stderr (won't interfere with JSON)
# echo "Executed: $HOOK_NAME from $CLAUDE_DIR (exit: $hook_exit_code)" >&2

exit $hook_exit_code