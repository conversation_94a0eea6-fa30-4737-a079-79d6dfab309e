---
description: Create enhancement PRD using PM expertise for competitive improvement initiatives
---

# BMAD Create Enhancement PRD Command

**Usage**: `*create-enhancement-prd [focus-area] [options]`

## Purpose

Creates enhancement Product Requirements Documents using PM expertise, leveraging BMAD principles to build upon existing project foundation while extending capabilities based on competitive analysis and user feedback.

### Key Benefits
- Leverages existing PRD and project documentation as foundation
- Extends current product capabilities with competitive improvements
- Reduces scope creep by maintaining focus on strategic enhancements
- Integrates seamlessly with BMAD competitive analysis and planning workflows

### When to Use
- After competitive analysis reveals improvement opportunities
- When user feedback identifies specific enhancement needs
- As part of product evolution and feature expansion planning
- When integrating new market requirements into existing products

## Implementation

### Step-by-Step Algorithm

#### 1. LEVER Framework Pre-Validation
Before executing, validate these principles:
- **L** - Leverage: Existing PRD, competitive analysis, and user research
- **E** - Extend: Current product capabilities with targeted enhancements
- **V** - Verify: Enhancement priorities against business goals and user value
- **E** - Eliminate: Feature bloat and non-strategic enhancement requests
- **R** - Reduce: Complexity by focusing on high-impact improvements

#### 2. Context Gathering
```bash
# Load project and competitive context
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "pm"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
CURRENT_PRD=$(cat docs/prd.md 2>/dev/null || echo "No existing PRD found")
COMPETITIVE_ANALYSIS=$(cat docs/competitive-analysis.md 2>/dev/null || echo "No competitive analysis found")
```

#### 3. Agent-Specific Processing
Adapt behavior based on active agent persona:

##### If PM Active
- Focus on business value alignment and stakeholder requirements
- Strategic enhancement prioritization and resource allocation
- Market positioning and competitive differentiation analysis

##### If Analyst Active
- Emphasize market research validation and competitive intelligence
- User behavior analysis and enhancement opportunity identification
- Data-driven prioritization and impact assessment

#### 4. Core Processing
- Load PM persona and enhancement PRD creation task
- Analyze existing PRD and competitive analysis for enhancement opportunities
- Create comprehensive enhancement PRD using established template
- Document enhancement priorities, success metrics, and implementation roadmap
- Generate stakeholder communication and approval framework

#### 5. Output Generation
- Structured enhancement PRD saved to `docs/enhancement-prd.md`
- Enhancement prioritization matrix and impact analysis
- Implementation roadmap with resource requirements
- Success metrics and validation criteria documentation

#### 6. Workflow Integration
- Updates workflow state to indicate enhancement planning completion
- Prepares context for story creation and development planning
- Integrates with existing product management and development workflows

## Quality Features / Validation Checklist

### BMAD Compliance Validation
- [ ] **LEVER Principles**: Builds upon existing PRD and competitive analysis
- [ ] **Agent Alignment**: Uses PM strategic planning and prioritization expertise
- [ ] **Workflow Integration**: Properly follows product enhancement workflow
- [ ] **Knowledge Capture**: Documents enhancement rationale and strategic decisions
- [ ] **Context Awareness**: Builds upon existing product and market context

### Technical Quality Gates
- [ ] **Input Validation**: Handles missing PRD or competitive analysis gracefully
- [ ] **Error Handling**: Provides guidance when prerequisite analysis is incomplete
- [ ] **Performance**: Efficient enhancement planning and prioritization process
- [ ] **Consistency**: Maintains product vision coherence through enhancements
- [ ] **Documentation**: Clear, actionable enhancement specifications

### Output Quality Assurance
- [ ] **Completeness**: Covers all major enhancement areas and priorities
- [ ] **Accuracy**: Enhancement specifications are feasible and well-researched
- [ ] **Relevance**: Enhancements align with business goals and user needs
- [ ] **Actionability**: Provides specific implementation guidance and priorities
- [ ] **Integration**: Seamlessly extends existing product requirements

### Knowledge Management Standards
- [ ] **Persistence**: Saves enhancement PRD to `docs/enhancement-prd.md`
- [ ] **Versioning**: Maintains enhancement planning history and iterations
- [ ] **Cross-Reference**: Links to competitive analysis and original PRD
- [ ] **Searchability**: Structured for easy reference during development
- [ ] **Agent Context**: Updates PM and development team knowledge

## Integration

### Workflow Hooks
- **Triggers**: `read-workflow.sh` for LEVER principles reminder
- **Updates**: `workflow-transition.sh` for enhancement planning completion
- **Notifications**: `notification-hook.sh` for enhancement PRD completion alerts
- **Context**: Updates `.claude/current-workflow-state.json`

### Knowledge Management Integration
- **Saves**: Enhancement PRD to `docs/enhancement-prd.md`
- **Updates**: Project context with enhancement priorities in `.ai/project-context.md`
- **Creates**: Enhancement tracking in `.ai/enhancements/` directory
- **Links**: Cross-references with competitive analysis and user research

### Agent Context Updates
- **PM**: Updates product planning and enhancement prioritization knowledge
- **All Agents**: Updates shared project enhancement context
- **Development**: Provides enhancement implementation priorities and specifications
- **QA**: Provides enhancement testing criteria and success validation

### Follow-up Command Preparation
Results prepare context for:
- `*create-next-story` - Development stories for enhancement implementation
- `*compare-competitor-features` - Continued competitive benchmarking
- `*update-knowledge` - Distribute enhancement specifications to all agents
- `*architect-design` - Architecture updates for enhancement support

## Related Commands

### Core Product Commands
- `*pm-prd` - Original PRD that serves as enhancement foundation
- `*compare-competitor-features` - Competitive analysis input for enhancements
- `*analyze-competitor-ux` - UX analysis for enhancement opportunities

### Workflow Commands
- `*create-next-story` - Implementation stories for enhancements
- `*plan-workflow` - Enhancement development workflow planning
- `*correct-course` - Strategic pivots based on enhancement insights

### Agent Commands
- `/switch-agent pm` - Optimal agent for enhancement PRD creation
- `/switch-agent analyst` - Research-focused enhancement analysis

### Knowledge Commands
- `/update-knowledge` - Distribute enhancement specifications to all agents
- `/memory-extract` - Extract enhancement insights and market intelligence

## Example Usage

### Basic Usage
```bash
# Create enhancement PRD based on competitive analysis
*create-enhancement-prd
```

### Advanced Usage
```bash
# Create enhancement PRD with specific competitive focus
*create-enhancement-prd mobile-ux --competitive-focus=feature-parity
```

### Agent-Specific Usage
```bash
# When PM agent is active:
*create-enhancement-prd --business-focused --roi-analysis

# When Analyst agent is active:
*create-enhancement-prd --research-driven --market-validation
```

### Workflow Integration Example
```bash
# Part of competitive improvement workflow:
/analyze-competitor-ux
/compare-competitor-features
*create-enhancement-prd  # ← Strategic enhancement planning
/update-knowledge
/create-next-story
/architect-design  # Update architecture for enhancements
```

## Notes

### Agent Personas
This command adapts its behavior based on the active agent:
- **PM**: Business-focused enhancement planning with ROI and resource analysis
- **Analyst**: Research-driven enhancement identification with market validation
- **Architect**: Technical feasibility assessment for enhancement requirements
- **Design Architect**: UX-focused enhancement planning with design impact analysis
- **QA**: Quality assurance considerations for enhancement testing

### LEVER Framework Emphasis
Every execution must demonstrate:
- **Leverage**: Existing PRD, competitive analysis, user research, and market intelligence
- **Extend**: Current product capabilities with strategically chosen enhancements
- **Verify**: Enhancement priorities against business goals, user value, and technical feasibility
- **Eliminate**: Feature requests that don't align with strategic objectives
- **Reduce**: Enhancement complexity while maximizing competitive advantage and user value

### Best Practices
- Run after comprehensive competitive analysis for optimal enhancement identification
- Prioritize enhancements based on user value and competitive differentiation
- Include resource requirements and timeline estimates for realistic planning
- Validate enhancement specifications with stakeholders before development planning
