---
description: [Brief description of what this command does and when to use it]
---

# BMAD [Command Name] Command

**Usage**: `*[command-name] [parameters] [options]`

## Purpose

[Explain why this command exists and what problem it solves. Reference BMAD principles and how this command fits into the overall methodology.]

### Key Benefits
- [Benefit 1: How it leverages existing BMAD patterns]
- [Benefit 2: How it extends current capabilities]
- [Benefit 3: How it reduces complexity or effort]
- [Benefit 4: How it integrates with BMAD workflows]

### When to Use
- [Scenario 1: Specific use case or trigger condition]
- [Scenario 2: Part of which workflow or agent activity]
- [Scenario 3: Problem type this command addresses]

## Implementation

### Step-by-Step Algorithm

#### 1. LEVER Framework Pre-Validation
Before executing, validate these principles:
- **L** - Leverage: [What existing patterns/tools/knowledge can we use?]
- **E** - Extend: [Can we extend existing capabilities rather than creating new ones?]
- **V** - Verify: [How can we verify our approach through existing patterns?]
- **E** - Eliminate: [Are we duplicating existing functionality?]
- **R** - Reduce: [What's the simplest approach that provides maximum value?]

#### 2. Context Gathering
```bash
# [Specific context gathering steps]
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "general"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
[Additional context gathering commands]
```

#### 3. Agent-Specific Processing
Adapt behavior based on active agent persona:

##### If [Agent Type] Active
- [Specific focus area for this agent]
- [Particular analysis or processing approach]
- [Specialized output or integration requirements]

##### If [Agent Type] Active
- [Different focus area for this agent]
- [Alternative analysis or processing approach]
- [Agent-specific output requirements]

#### 4. Core Processing
- [Main processing step 1: Description and commands]
- [Main processing step 2: Description and commands]
- [Main processing step 3: Description and commands]
- [Main processing step 4: Description and commands]

#### 5. Output Generation
- [How results are formatted and presented]
- [Where outputs are saved]
- [How outputs integrate with BMAD knowledge system]

#### 6. Workflow Integration
- [How this command updates workflow state]
- [What follow-up commands or workflows are triggered]
- [How results feed into subsequent BMAD processes]

## Quality Features / Validation Checklist

### BMAD Compliance Validation
- [ ] **LEVER Principles**: Demonstrates leverage, extension, verification, elimination, reduction
- [ ] **Agent Alignment**: Respects current agent persona and expertise areas
- [ ] **Workflow Integration**: Properly integrates with BMAD workflow states and transitions
- [ ] **Knowledge Capture**: Documents insights and results for future reference
- [ ] **Context Awareness**: Uses existing project context and builds upon it

### Technical Quality Gates
- [ ] **Input Validation**: Handles edge cases and invalid inputs gracefully
- [ ] **Error Handling**: Provides clear error messages and recovery suggestions
- [ ] **Performance**: Executes efficiently without unnecessary resource usage
- [ ] **Consistency**: Maintains consistent behavior across different scenarios
- [ ] **Documentation**: Generates clear, actionable outputs

### Output Quality Assurance
- [ ] **Completeness**: Addresses all aspects of the command's purpose
- [ ] **Accuracy**: Information is correct and up-to-date
- [ ] **Relevance**: Results are pertinent to current project and agent context
- [ ] **Actionability**: Provides specific, implementable recommendations
- [ ] **Integration**: Results properly integrate with existing BMAD artifacts

### Knowledge Management Standards
- [ ] **Persistence**: Results saved to appropriate `.ai/` directory locations
- [ ] **Versioning**: Updates maintain version history and change tracking
- [ ] **Cross-Reference**: Links to related artifacts and dependencies
- [ ] **Searchability**: Structured for easy retrieval and reference
- [ ] **Agent Context**: Updates all relevant agent knowledge bases

## Integration

### Workflow Hooks
- **Triggers**: `read-workflow.sh` for LEVER principles reminder
- **Updates**: `workflow-transition.sh` for state management
- **Notifications**: `notification-hook.sh` for logging and alerts
- **Context**: Updates `.claude/current-workflow-state.json`

### Knowledge Management Integration
- **Saves**: Results to `.ai/[command-name]-[timestamp].md`
- **Updates**: Project context in `.ai/project-context.md`
- **Creates**: Issue tracking in `.ai/issues/` if needed
- **Links**: Cross-references with related artifacts

### Agent Context Updates
- **Current Agent**: Updates active agent's knowledge base
- **All Agents**: Updates shared project knowledge
- **Specialized**: Updates domain-specific knowledge stores
- **Workflow**: Prepares context for handoffs and transitions

### Follow-up Command Preparation
Results prepare context for:
- `*[related-command-1]` - [Description of when and why]
- `*[related-command-2]` - [Description of when and why]
- `*[related-command-3]` - [Description of when and why]
- `*[workflow-command]` - [Description of workflow integration]

## Related Commands

### Core Analysis Commands
- `*analyze` - [How it relates to this command]
- `*debug` - [When to use debug vs this command]
- `*codereview` - [Complementary analysis capabilities]

### Workflow Commands
- `*[workflow-1]` - [How this command fits into workflow]
- `*[workflow-2]` - [Alternative or complementary workflow]
- `*[workflow-3]` - [Follow-up or preparatory workflow]

### Agent Commands
- `/switch-agent [agent-type]` - [When to switch agents for this command]
- `/[agent-specific-command]` - [Specialized agent commands that work with this]

### Knowledge Commands
- `/update-knowledge` - [When to update knowledge after this command]
- `/memory-extract` - [How memory extraction relates to this command]

## Example Usage

### Basic Usage
```bash
# [Simple example with common parameters]
*[command-name] [basic-parameters]
```

### Advanced Usage
```bash
# [Complex example with multiple options]
*[command-name] [advanced-parameters] --[option1] --[option2]
```

### Agent-Specific Usage
```bash
# [Example showing different behavior per agent]
# When Architect agent is active:
*[command-name] [parameters] --focus=architecture

# When Developer agent is active:
*[command-name] [parameters] --focus=implementation
```

### Workflow Integration Example
```bash
# [Example showing how this fits into larger workflows]
# Part of project initialization:
/project-init
# ... (other workflow steps)
*[command-name] [parameters]  # ← This command's role in workflow
# ... (subsequent workflow steps)
```

## Notes

### Agent Personas
This command adapts its behavior based on the active agent:
- **Architect**: [Specific adaptations and focus areas]
- **Developer**: [Specific adaptations and focus areas]
- **Analyst**: [Specific adaptations and focus areas]
- **PM**: [Specific adaptations and focus areas]
- **QA**: [Specific adaptations and focus areas]

### LEVER Framework Emphasis
Every execution must demonstrate:
- **Leverage**: [Specific examples of leveraging existing assets]
- **Extend**: [How this extends rather than replaces]
- **Verify**: [Verification mechanisms built into the command]
- **Eliminate**: [What redundancy this command eliminates]
- **Reduce**: [How this reduces complexity or effort]

### Best Practices
- [Practice 1: Specific guidance for optimal usage]
- [Practice 2: Common pitfalls to avoid]
- [Practice 3: Integration recommendations]
- [Practice 4: Maintenance and update guidelines]
