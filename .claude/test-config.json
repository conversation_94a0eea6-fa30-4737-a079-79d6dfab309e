{"testingMode": "visible", "defaultPort": 3000, "browser": {"type": "chromium", "viewport": {"width": 1280, "height": 720}, "userAgent": "PIB-Test-Runner/1.0", "headless": false}, "timeouts": {"navigation": 30000, "element": 10000, "assertion": 5000}, "retryConfig": {"maxRetries": 3, "retryDelay": 1000}, "environment": {"baseUrl": "http://localhost:3000", "useEmulators": true, "emulatorPorts": {"auth": 9099, "firestore": 8080, "storage": 9199, "functions": 5001}}, "credentials": {"enabled": true, "storage": "encrypted", "testUser": {"email": "<EMAIL>", "password": "MPStander@3"}}, "screenshots": {"onFailure": true, "directory": "./test-results/screenshots"}, "video": {"enabled": false, "directory": "./test-results/videos"}}