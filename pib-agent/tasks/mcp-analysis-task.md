# MCP Analysis Task

## Overview
Use MCP (Model Context Protocol) tools to perform deep analysis of code, architecture, or complex problems.

## Available MCP Analysis Tools

### For Complex Problem Solving
- **`*mcp-thinkdeep`** - Multi-stage investigation with expert validation
- **`*mcp-planner`** - Sequential planning for complex tasks
- **`*mcp-consensus`** - Multi-model validation for critical decisions

### For Code Analysis
- **`*mcp-analyze`** - Comprehensive code and architecture analysis
- **`*mcp-debug`** - Root cause analysis for bugs
- **`*mcp-tracer`** - Code flow and dependency tracing
- **`*mcp-codereview`** - Multi-perspective code review
- **`*mcp-refactor`** - Identify improvement opportunities
- **`*mcp-secaudit`** - Security vulnerability assessment

### For Documentation & Testing
- **`*mcp-docgen`** - Generate comprehensive documentation
- **`*mcp-testgen`** - Create thorough test suites
- **`*mcp-precommit`** - Validate changes before committing

## Task Execution Patterns

### Pattern 1: Complex Problem Analysis
```markdown
# Bill (Orchestrator) analyzing a complex requirement
*Bill: "This authentication system needs deep analysis..."

# Step 1: Use thinkdeep for comprehensive planning
*mcp-thinkdeep "Design secure authentication with SSO, MFA, social login, and session management"
→ Provides 5-step analysis with expert validation

# Step 2: Use planner for implementation sequence
*mcp-planner "Create step-by-step implementation plan for authentication system"
→ Creates sequential, revisable plan

# Step 3: Get consensus on approach
*mcp-consensus "Authentication architecture: JWT vs Session-based approach"
→ Multi-model perspectives and recommendations
```

### Pattern 2: Code Quality Review
```markdown
# Code Reviewer examining a pull request
*Reviewer: "Running comprehensive MCP code review..."

# Step 1: Initial code review
*mcp-codereview "/Users/<USER>/src/auth/"
→ Multi-perspective analysis of code quality

# Step 2: Security audit
*mcp-secaudit "/Users/<USER>/src/auth/"
→ Identifies security vulnerabilities

# Step 3: Refactoring opportunities
*mcp-refactor "/Users/<USER>/src/auth/login.js"
→ Suggests improvements and modernization
```

### Pattern 3: Debugging Complex Issues
```markdown
# Dev troubleshooting a bug
*Dev: "Using MCP to debug this issue..."

# Step 1: Root cause analysis
*mcp-debug "Users getting logged out randomly after 5 minutes"
→ Systematic investigation with hypothesis testing

# Step 2: Trace code execution
*mcp-tracer "Session.validateToken method"
→ Maps execution flow and dependencies

# Step 3: Generate tests for the fix
*mcp-testgen "Session timeout edge cases"
→ Creates comprehensive test coverage
```

## Integration with Agent Workflows

### For Bill (PM/Orchestrator)
```markdown
*Bill orchestrate "implement payment processing"
  → Automatically uses *mcp-thinkdeep for complex planning
  → Uses *mcp-planner for task breakdown
  → Uses *mcp-consensus for architectural decisions
```

### For Architect
```markdown
*Architect design "microservices migration"
  → Uses *mcp-analyze for current architecture assessment
  → Uses *mcp-secaudit for security implications
  → Uses *mcp-refactor for modernization strategy
```

### For Dev
```markdown
*Dev implement "real-time notifications"
  → Uses *mcp-debug when issues arise
  → Uses *mcp-tracer to understand existing code
  → Uses *mcp-testgen for test creation
```

### For QA
```markdown
*QA test "checkout flow"
  → Uses *mcp-testgen for comprehensive test cases
  → Uses *mcp-codereview for test quality
  → Uses *mcp-analyze for performance implications
```

## Best Practices

1. **Start with High-Level Analysis**: Use `*mcp-thinkdeep` for complex problems
2. **Layer Multiple Tools**: Combine tools for comprehensive analysis
3. **Document Sessions**: MCP sessions are logged in `.ai/mcp-sessions/`
4. **Use Appropriate Thinking Modes**:
   - `minimal/low` - Simple tasks
   - `medium` - Standard analysis (default)
   - `high` - Complex problems
   - `max` - Critical decisions

5. **Leverage Multi-Model Intelligence**: Use consensus for important decisions

## Output Examples

### ThinkDeep Output Structure
```
Step 1: Initial Analysis
- Understanding requirements
- Identifying constraints

Step 2: Investigation
- Exploring solutions
- Analyzing trade-offs

Step 3: Design
- Proposing architecture
- Defining components

Step 4: Risk Assessment
- Security considerations
- Performance implications

Step 5: Implementation Plan
- Detailed steps
- Resource requirements

Expert Validation:
- Recommendations
- Alternative approaches
```

### Code Review Output Structure
```
Security Issues:
- [CRITICAL] SQL injection vulnerability in line 45
- [HIGH] Missing input validation

Code Quality:
- [MEDIUM] Function too complex (cyclomatic complexity: 15)
- [LOW] Inconsistent naming convention

Performance:
- [HIGH] N+1 query problem in getUserData()

Recommendations:
- Implement parameterized queries
- Add input validation layer
- Optimize database queries
```

## When to Use MCP Analysis

**Use MCP Tools When:**
- Problem complexity is high
- Multiple perspectives needed
- Security implications exist
- Architecture decisions required
- Debugging complex issues
- Need comprehensive test coverage

**Skip MCP Tools When:**
- Simple, straightforward tasks
- Clear implementation path
- Minimal risk or complexity
- Time-critical quick fixes

## Session Logging

All MCP sessions are automatically logged:
```
.ai/mcp-sessions/
├── 2024-06-24-thinkdeep-payment-processing/
│   ├── session.json
│   ├── step-*.md
│   └── expert-analysis.md
├── 2024-06-24-debug-session-timeout/
│   ├── investigation-log.md
│   ├── hypothesis-tracking.json
│   └── solution.md
└── 2024-06-24-codereview-auth-module/
    ├── review-findings.md
    ├── security-report.md
    └── refactor-suggestions.md
```