# Gmail API Integration Testing Plan

## Overview
This document outlines the comprehensive testing strategy for Gmail API integration, including test execution instructions and expected outcomes.

## Test Suite Structure

### 1. Unit Tests
**Location**: `tests/unit/`
**Framework**: Vitest
**Coverage Target**: 90%+

#### Gmail API Service Tests (`tests/unit/services/gmail-api-service.test.ts`)
- **Authentication & Token Management**
  - Token validation and refresh
  - Service initialization
  - Authentication status checks
  - Error handling for expired tokens

- **Message Operations**
  - List messages with various filters
  - Retrieve individual messages
  - Batch message operations
  - Search with complex queries
  - Message modification (labels)
  - Message deletion

- **Label Management**
  - List labels
  - Create/update/delete labels
  - System vs user label handling

- **Rate Limiting**
  - Request throttling (50ms intervals)
  - Concurrent request handling
  - API quota management

- **Error Handling**
  - Gmail API error responses (401, 403, 429, 500)
  - Network failures
  - Malformed data handling

#### Gmail Composable Tests (`tests/unit/composables/useGmail.test.ts`)
- **State Management**
  - Reactive state updates
  - Account switching
  - Error state management
  - Loading states

- **API Integration**
  - Credential management
  - API call orchestration
  - Response caching
  - Token refresh handling

- **Helper Methods**
  - Mark as read/unread
  - Star/unstar messages
  - Archive operations
  - Bulk operations

- **Search Query Building**
  - Complex query construction
  - Parameter validation
  - Search option mapping

#### API Route Tests (`tests/unit/server/api/integrations/gmail/`)
- **Authentication Endpoints**
  - OAuth status validation
  - Token verification
  - Profile retrieval

- **Message Endpoints**
  - Message listing with filters
  - Individual message retrieval
  - Message modification
  - Message deletion
  - Search functionality

- **Label Endpoints**
  - Label listing
  - Label operations

- **Error Handling**
  - Parameter validation
  - Error response formatting
  - Rate limiting responses

### 2. Integration Tests
**Location**: `tests/integration/`
**Framework**: Vitest
**Environment**: Mocked Firebase + API

#### Gmail Integration Tests (`tests/integration/gmail-integration.test.ts`)
- **End-to-End Workflows**
  - Complete authentication flow
  - Message retrieval and display
  - Label management workflow
  - Message operations (CRUD)

- **Data Flow Validation**
  - Composable → API → Service interaction
  - State synchronization
  - Error propagation
  - Cache management

- **Account Management**
  - Gmail account detection
  - Account switching
  - Multi-account scenarios
  - Credential handling

### 3. End-to-End Tests
**Location**: `tests/e2e/`
**Framework**: Playwright
**Environment**: Full browser automation

#### Gmail Inbox Workflows (`tests/e2e/gmail-inbox-workflows.spec.ts`)
- **UI Integration**
  - Gmail account display in inbox
  - Message list rendering
  - Message detail view
  - Filter and search UI

- **User Interactions**
  - Message selection
  - Star/unstar operations
  - Message deletion
  - Reply functionality
  - Sync operations

- **Responsive Design**
  - Mobile layout testing
  - Sidebar behavior
  - Touch interactions

- **Error Scenarios**
  - Network timeout handling
  - API error display
  - Retry mechanisms
  - Authorization failures

### 4. Performance Tests
**Location**: `tests/performance/`
**Framework**: Vitest with performance measurement

#### Performance Benchmarks (`tests/performance/gmail-performance.test.ts`)
- **Response Time Requirements**
  - Message listing: < 2 seconds
  - Message details: < 1 second
  - Labels loading: < 500ms
  - Profile loading: < 300ms

- **Batch Operations**
  - 100 message retrieval: < 5 seconds
  - 50 message label updates: < 2 seconds
  - 250 message processing: < 15 seconds

- **Rate Limiting Performance**
  - Request throttling validation
  - Concurrent operation handling
  - Memory usage monitoring

- **Large Data Handling**
  - 5MB message processing: < 3 seconds
  - Pagination efficiency
  - Memory leak prevention

### 5. Security Tests
**Location**: `tests/security/`
**Framework**: Vitest with security validation

#### Security Validation (`tests/security/gmail-security.test.ts`)
- **Authentication Security**
  - Token validation
  - Authorization checks
  - Session management
  - CSRF protection

- **Input Validation**
  - XSS prevention
  - SQL injection prevention
  - Path traversal prevention
  - Parameter sanitization

- **Data Protection**
  - Sensitive data masking
  - Error message sanitization
  - Secure token storage
  - Data retention policies

- **Rate Limiting & DoS Protection**
  - Request throttling
  - Concurrent request limits
  - Payload size limits
  - Resource exhaustion prevention

## Test Execution Instructions

### Prerequisites
```bash
# Install dependencies
pnpm install

# Start Firebase emulators (for integration tests)
pnpm emulators

# Ensure test environment variables are set
export NUXT_PUBLIC_USE_FIREBASE_EMULATOR=true
export FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
export FIRESTORE_EMULATOR_HOST=localhost:8080
```

### Running Tests

#### Unit Tests
```bash
# Run all unit tests
pnpm test:unit

# Run specific test suites
pnpm vitest tests/unit/services/gmail-api-service.test.ts
pnpm vitest tests/unit/composables/useGmail.test.ts
pnpm vitest tests/unit/server/api/integrations/gmail/

# Run with coverage
pnpm test:unit --coverage
```

#### Integration Tests
```bash
# Run integration tests
pnpm test:integration

# Run specific integration test
pnpm vitest tests/integration/gmail-integration.test.ts
```

#### End-to-End Tests
```bash
# Run all e2e tests
pnpm test:e2e

# Run specific browser
pnpm playwright test --project=chromium

# Run with UI mode
pnpm playwright test --ui
```

#### Performance Tests
```bash
# Run performance benchmarks
pnpm vitest tests/performance/gmail-performance.test.ts

# Run with detailed timing
pnpm vitest tests/performance/ --reporter=verbose
```

#### Security Tests
```bash
# Run security validation
pnpm vitest tests/security/gmail-security.test.ts

# Run with detailed output
pnpm vitest tests/security/ --reporter=verbose
```

#### All Tests
```bash
# Run complete test suite
pnpm test

# Run with coverage and reports
pnpm test:ci
```

## Test Data and Mocking

### Mock Data Sources
- **Gmail Messages**: Generated test messages with various states (read/unread, starred, labeled)
- **Gmail Labels**: System and user labels with proper hierarchy
- **Gmail Profile**: Mock profile data with realistic message counts
- **Error Scenarios**: Comprehensive error response mocking

### Test Environment Setup
- **Firebase Emulators**: Local emulation for Firestore operations
- **API Mocking**: Comprehensive Gmail API response mocking
- **Network Simulation**: Timeout and failure scenario testing
- **Authentication Mocking**: OAuth flow simulation

## Success Criteria

### Coverage Targets
- **Overall Coverage**: ≥ 90%
- **Critical Components**: ≥ 95%
- **Branch Coverage**: ≥ 90%
- **Function Coverage**: ≥ 90%

### Performance Benchmarks
- **API Response Times**: All under defined thresholds
- **UI Responsiveness**: < 100ms for user interactions
- **Memory Usage**: No memory leaks detected
- **Concurrent Operations**: Proper handling without degradation

### Security Validation
- **Input Sanitization**: All user inputs properly validated
- **Authentication**: Secure token handling and validation
- **Error Handling**: No sensitive data leakage
- **Rate Limiting**: Proper protection against abuse

### Functional Requirements
- **Gmail Authentication**: Seamless OAuth integration
- **Message Operations**: Complete CRUD functionality
- **Search & Filtering**: Advanced search capabilities
- **UI Integration**: Proper inbox.vue integration
- **Error Recovery**: Graceful error handling and recovery

## Continuous Integration

### Pre-commit Hooks
```bash
# Run on every commit
pnpm lint
pnpm typecheck
pnpm test:unit --run

# Run on push to main
pnpm test:integration --run
pnpm test:security --run
```

### CI Pipeline
```bash
# Full test suite for pull requests
pnpm test:ci
pnpm test:e2e:ci
pnpm security:audit
```

### Quality Gates
- All tests must pass
- Coverage thresholds must be met
- Performance benchmarks must pass
- Security validation must pass
- No critical vulnerabilities detected

## Monitoring and Reporting

### Test Reports
- **HTML Coverage Report**: Generated in `coverage/`
- **JSON Test Results**: Available in `test-results.json`
- **Performance Metrics**: Logged with timestamps
- **Security Audit**: Detailed vulnerability assessment

### Metrics Tracking
- Test execution time trends
- Coverage percentage over time
- Performance benchmark trends
- Error rate monitoring

## Troubleshooting

### Common Issues
1. **Firebase Emulator Connection**: Ensure emulators are running
2. **Mock Data Inconsistencies**: Verify test data generation
3. **Rate Limiting in Tests**: Check test isolation and cleanup
4. **Authentication Mocking**: Validate token format and expiration

### Debug Commands
```bash
# Debug specific test
pnpm vitest tests/unit/services/gmail-api-service.test.ts --reporter=verbose

# Debug with browser tools (e2e)
pnpm playwright test --debug

# Check test coverage details
pnpm test:unit --coverage --reporter=html
```

## Documentation Updates

### Required Documentation
- API endpoint documentation updates
- Composable usage examples
- Security configuration guide
- Performance optimization guide
- Troubleshooting documentation

### Code Comments
- Critical security validations
- Performance optimization notes
- Complex business logic explanations
- Integration point documentation
