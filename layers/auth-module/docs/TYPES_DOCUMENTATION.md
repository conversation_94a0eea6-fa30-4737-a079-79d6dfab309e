# Auth Module Types

This directory contains all TypeScript type definitions for the auth module.

## Profile Types (`profile.ts`)

The profile types define the structure for user profiles within workspaces. Key features:

### Main Types
- `Profile` - Complete profile interface with all fields
- `CreateProfileData` - Data required to create a new profile
- `UpdateProfileData` - Data for updating existing profiles
- `ProfileSettings` - Privacy and notification settings

### Enums
- `ProfileVisibility` - Public, WorkspaceOnly, or Private
- `SkillProficiency` - Beginner to Expert levels
- `ExperienceType` - Full-time, Contract, Freelance, etc.
- `LanguageProficiency` - Native to Basic levels

### Supporting Interfaces
- `Experience` - Work history entries
- `Skill` - Technical skills with proficiency levels
- `Language` - Language proficiencies
- `Tool` - Software/tool proficiencies
- `SocialLink` - Social media profiles
- `Recommendation` - Testimonials from other users
- `ProfileView` - Tracking who viewed the profile

### Helper Functions
- `getProfileId(userId, workspaceId)` - Generate profile document ID
- `parseProfileId(profileId)` - Extract userId and workspaceId
- `getProfileCompletionScore(profile)` - Calculate profile completeness (0-100)

### Firebase Compatibility
All field names use camelCase to ensure compatibility with Firebase security rules and queries.

### Document ID Format
Profile documents use the format: `{userId}_{workspaceId}`

This allows for:
- One profile per user per workspace
- Easy querying by userId or workspaceId
- Consistent document references
