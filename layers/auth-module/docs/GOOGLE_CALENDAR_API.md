# Google Calendar Server-Side API Routes

This directory contains the server-side API routes for Google Calendar integration following the LEVER principles and existing project patterns.

## Overview

These API routes provide comprehensive Google Calendar functionality including:
- Event management (CRUD operations)
- Calendar listing and access
- Token refresh handling
- Error handling and retry logic
- Integration with local Firestore database

## Authentication

All routes require:
1. Valid user session (authenticated user)
2. Active Google Calendar integration in the user's workspace
3. Valid OAuth tokens (access and refresh tokens)

Routes automatically handle token refresh when needed.

## API Endpoints

### Events API

#### List Events
**GET** `/api/integrations/google-calendar/events`

Query Parameters:
- `calendarId` (optional): Specific calendar ID (defaults to primary)
- `timeMin` (optional): Start time filter (ISO 8601)
- `timeMax` (optional): End time filter (ISO 8601)
- `maxResults` (optional): Max events to return (1-2500, default: 250)
- `singleEvents` (optional): Expand recurring events (default: true)
- `orderBy` (optional): Sort order - 'startTime' or 'updated'
- `showDeleted` (optional): Include deleted events (default: false)
- `syncToken` (optional): Sync token for incremental sync
- `pageToken` (optional): Pagination token

Response:
```json
{
  "success": true,
  "data": {
    "events": [...],
    "nextPageToken": "...",
    "nextSyncToken": "...",
    "timeZone": "America/New_York"
  },
  "pagination": {
    "nextPageToken": "...",
    "nextSyncToken": "..."
  }
}
```

#### Create Event
**POST** `/api/integrations/google-calendar/events`

Request Body:
```json
{
  "title": "Meeting Title",
  "description": "Optional description",
  "location": "Optional location",
  "startDate": "2024-01-15T10:00:00Z",
  "endDate": "2024-01-15T11:00:00Z",
  "allDay": false,
  "timeZone": "America/New_York",
  "attendees": [
    {
      "email": "<EMAIL>",
      "name": "Attendee Name",
      "optional": false
    }
  ],
  "reminders": {
    "useDefault": false,
    "overrides": [
      {
        "method": "email",
        "minutes": 15
      }
    ]
  },
  "visibility": "default",
  "status": "confirmed",
  "sendUpdates": "all"
}
```

#### Get Specific Event
**GET** `/api/integrations/google-calendar/events/{eventId}`

Query Parameters:
- `calendarId` (optional): Specific calendar ID
- `maxAttendees` (optional): Max attendees to return
- `timeZone` (optional): Time zone for response

#### Update Event
**PUT** `/api/integrations/google-calendar/events/{eventId}`

Request Body: (All fields optional, only provided fields will be updated)
```json
{
  "title": "Updated Title",
  "description": "Updated description",
  "startDate": "2024-01-15T10:30:00Z",
  "endDate": "2024-01-15T11:30:00Z",
  "sendUpdates": "all"
}
```

#### Delete Event
**DELETE** `/api/integrations/google-calendar/events/{eventId}`

Query Parameters:
- `calendarId` (optional): Specific calendar ID
- `sendUpdates` (optional): 'all', 'externalOnly', or 'none'

### Calendars API

#### List Calendars
**GET** `/api/integrations/google-calendar/calendars`

Query Parameters:
- `maxResults` (optional): Max calendars to return (1-250, default: 250)
- `showDeleted` (optional): Include deleted calendars (default: false)
- `showHidden` (optional): Include hidden calendars (default: false)
- `minAccessRole` (optional): Minimum access role filter
- `pageToken` (optional): Pagination token
- `syncToken` (optional): Sync token for incremental sync

Response:
```json
{
  "success": true,
  "data": {
    "calendars": {
      "primary": [...],
      "owned": [...],
      "subscribed": [...],
      "shared": [...],
      "all": [...]
    },
    "stats": {
      "total": 5,
      "primary": 1,
      "owned": 2,
      "subscribed": 1,
      "shared": 1
    },
    "pagination": {
      "nextPageToken": "...",
      "nextSyncToken": "..."
    }
  }
}
```

## Error Handling

All routes implement comprehensive error handling:

### HTTP Status Codes
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (missing/invalid session or expired tokens)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found (integration, calendar, or event not found)
- `409`: Conflict (event conflicts or duplicates)
- `410`: Gone (event has been deleted)
- `412`: Precondition Failed (version mismatch)
- `500`: Internal Server Error

### Common Error Responses
```json
{
  "statusCode": 401,
  "statusMessage": "Google Calendar authorization expired. Please reconnect."
}
```

## Rate Limiting and Retry Logic

The routes implement:
- Automatic token refresh on 401 errors
- Respect for Google API rate limits
- Proper error propagation
- Firestore integration sync

## Local Database Integration

All event operations sync with local Firestore collections:
- `calendar_integrations`: Store OAuth tokens and integration data
- `calendar_events`: Local copy of calendar events for offline access
- `calendar_event_deletions`: Audit trail for deleted events

## Usage Examples

### Frontend Integration
```typescript
// List events
const events = await $fetch('/api/integrations/google-calendar/events', {
  query: {
    timeMin: new Date().toISOString(),
    maxResults: 50
  }
})

// Create event
const newEvent = await $fetch('/api/integrations/google-calendar/events', {
  method: 'POST',
  body: {
    title: 'Team Meeting',
    startDate: '2024-01-15T10:00:00Z',
    endDate: '2024-01-15T11:00:00Z',
    attendees: [
      { email: '<EMAIL>', name: 'Team' }
    ]
  }
})

// Update event
const updatedEvent = await $fetch(`/api/integrations/google-calendar/events/${eventId}`, {
  method: 'PUT',
  body: {
    title: 'Updated Meeting Title',
    startDate: '2024-01-15T10:30:00Z'
  }
})

// Delete event
await $fetch(`/api/integrations/google-calendar/events/${eventId}`, {
  method: 'DELETE',
  query: { sendUpdates: 'all' }
})

// List calendars
const calendars = await $fetch('/api/integrations/google-calendar/calendars')
```

## Environment Variables Required

Ensure these environment variables are set:
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret

## Security Considerations

1. **Token Management**: Access tokens are automatically refreshed
2. **Session Validation**: All routes validate user sessions
3. **Workspace Isolation**: Data is scoped to user's current workspace
4. **Audit Logging**: Event deletions are logged for compliance
5. **Input Validation**: All inputs are validated before processing
6. **Error Sanitization**: Sensitive information is not exposed in errors

## Testing

Test the routes using curl or your preferred HTTP client:

```bash
# List events (requires authentication)
curl -X GET "http://localhost:3000/api/integrations/google-calendar/events" \
  -H "Cookie: auth:session=<session_cookie>"

# Create event
curl -X POST "http://localhost:3000/api/integrations/google-calendar/events" \
  -H "Content-Type: application/json" \
  -H "Cookie: auth:session=<session_cookie>" \
  -d '{
    "title": "Test Event",
    "startDate": "2024-01-15T10:00:00Z",
    "endDate": "2024-01-15T11:00:00Z"
  }'
```

## Integration Notes

These routes follow the existing project patterns:
- Use Firebase Admin SDK for Firestore operations
- Follow the same session management as other auth routes
- Implement the same error handling patterns
- Use the existing OAuth service architecture
- Maintain workspace isolation like other integrations
