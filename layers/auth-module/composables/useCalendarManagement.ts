import type { Unsubscribe } from 'firebase/firestore'
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  onSnapshot,
  query,
  serverTimestamp,
  updateDoc,
  where,
} from 'firebase/firestore'
import { computed, onUnmounted, readonly, ref, watch } from 'vue'
import { useFirebase } from './firebase'

export interface Calendar {
  id: string
  name: string
  description?: string
  color: string
  isDefault: boolean
  isVisible: boolean
  userId: string
  workspaceId: string

  // Integration details
  integrationId?: string
  externalCalendarId?: string
  provider?: 'google' | 'outlook' | 'apple' | null

  // Metadata
  createdAt: Date
  updatedAt: Date
}

export interface CalendarCreationData {
  name: string
  description?: string
  color: string
  isDefault?: boolean
}

export function useCalendarManagement() {
  const { firestore } = useFirebase()
  const { currentProfile } = useAuth()
  const toaster = useNuiToasts()

  // State
  const calendars = ref<Calendar[]>([])
  const selectedCalendarId = ref<string>('all')
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  let unsubscribe: Unsubscribe | null = null

  // Computed properties
  const defaultCalendar = computed(() =>
    calendars.value.find(calendar => calendar.isDefault),
  )

  const visibleCalendars = computed(() =>
    calendars.value.filter(calendar => calendar.isVisible),
  )

  const selectedCalendar = computed(() =>
    calendars.value.find(calendar => calendar.id === selectedCalendarId.value),
  )

  // Convert Firestore document to Calendar
  function firestoreToCalendar(doc: any): Calendar {
    const data = doc.data()
    return {
      id: doc.id,
      name: data.name || 'Untitled Calendar',
      description: data.description || '',
      color: data.color || '#3B82F6',
      isDefault: data.isDefault || false,
      isVisible: data.isVisible !== false, // Default to true if not specified
      userId: data.userId,
      workspaceId: data.workspaceId,
      integrationId: data.integrationId,
      externalCalendarId: data.externalCalendarId,
      provider: data.provider,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
    }
  }

  // Setup real-time subscription to calendars
  function setupSubscription() {
    if (!currentProfile.value?.workspaceId || !currentProfile.value?.userId) {
      console.warn('[useCalendarManagement] No workspace or user context for subscription')
      return
    }

    try {
      const calendarsCollection = collection(firestore, 'calendars')
      const calendarsQuery = query(
        calendarsCollection,
        where('workspaceId', '==', currentProfile.value.workspaceId),
        where('userId', '==', currentProfile.value.userId),
      )

      unsubscribe = onSnapshot(calendarsQuery, (snapshot) => {
        calendars.value = snapshot.docs.map(firestoreToCalendar)
        error.value = null
      }, (err) => {
        console.error('Error subscribing to calendars:', err)
        error.value = `Failed to sync calendars: ${err.message}`
        toaster.add({
          title: 'Sync Error',
          description: 'Failed to sync calendars in real-time',
          color: 'danger',
          icon: 'heroicons:exclamation-triangle',
        })
      })
    }
    catch (err: any) {
      console.error('Error subscribing to calendars:', err)
      error.value = `Failed to setup calendar sync: ${err.message}`
    }
  }

  // Create a new calendar
  async function createCalendar(data: CalendarCreationData): Promise<string> {
    if (!currentProfile.value?.workspaceId || !currentProfile.value?.userId) {
      throw new Error('No workspace or user context available')
    }

    isLoading.value = true
    error.value = null

    try {
      const calendarsCollection = collection(firestore, 'calendars')
      const calendarData = {
        ...data,
        userId: currentProfile.value.userId,
        workspaceId: currentProfile.value.workspaceId,
        isVisible: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      }

      const docRef = await addDoc(calendarsCollection, calendarData)

      toaster.add({
        title: 'Calendar Created',
        description: `Calendar "${data.name}" has been created successfully`,
        color: 'green',
        icon: 'heroicons:check-circle',
      })

      return docRef.id
    }
    catch (err: any) {
      console.error('[useCalendarManagement] Failed to create calendar:', err)
      error.value = `Failed to create calendar: ${err.message}`
      toaster.add({
        title: 'Error',
        description: 'Failed to create calendar',
        color: 'danger',
        icon: 'heroicons:x-circle',
      })
      throw err
    }
    finally {
      isLoading.value = false
    }
  }

  // Update a calendar
  async function updateCalendar(calendarId: string, updates: Partial<Calendar>): Promise<void> {
    isLoading.value = true
    error.value = null

    try {
      const calendarDoc = doc(firestore, 'calendars', calendarId)
      const updateData = {
        ...updates,
        updatedAt: serverTimestamp(),
      }

      await updateDoc(calendarDoc, updateData)

      toaster.add({
        title: 'Calendar Updated',
        description: 'Calendar has been updated successfully',
        color: 'green',
        icon: 'heroicons:check-circle',
      })
    }
    catch (err: any) {
      console.error('[useCalendarManagement] Failed to update calendar:', err)
      error.value = `Failed to update calendar: ${err.message}`
      toaster.add({
        title: 'Error',
        description: 'Failed to update calendar',
        color: 'danger',
        icon: 'heroicons:x-circle',
      })
      throw err
    }
    finally {
      isLoading.value = false
    }
  }

  // Delete a calendar
  async function deleteCalendar(calendarId: string): Promise<void> {
    isLoading.value = true
    error.value = null

    try {
      const calendarDoc = doc(firestore, 'calendars', calendarId)
      await deleteDoc(calendarDoc)

      // If the deleted calendar was selected, switch to 'all'
      if (selectedCalendarId.value === calendarId) {
        selectedCalendarId.value = 'all'
      }

      toaster.add({
        title: 'Calendar Deleted',
        description: 'Calendar has been deleted successfully',
        color: 'green',
        icon: 'heroicons:check-circle',
      })
    }
    catch (err: any) {
      console.error('[useCalendarManagement] Failed to delete calendar:', err)
      error.value = `Failed to delete calendar: ${err.message}`
      toaster.add({
        title: 'Error',
        description: 'Failed to delete calendar',
        color: 'danger',
        icon: 'heroicons:x-circle',
      })
      throw err
    }
    finally {
      isLoading.value = false
    }
  }

  // Toggle calendar visibility
  async function toggleCalendarVisibility(calendarId: string): Promise<void> {
    const calendar = calendars.value.find(c => c.id === calendarId)
    if (!calendar) {
      throw new Error('Calendar not found')
    }

    await updateCalendar(calendarId, { isVisible: !calendar.isVisible })
  }

  // Set calendar as default
  async function setDefaultCalendar(calendarId: string): Promise<void> {
    // First, unset any existing default calendar
    const currentDefault = defaultCalendar.value
    if (currentDefault && currentDefault.id !== calendarId) {
      await updateCalendar(currentDefault.id, { isDefault: false })
    }

    // Set the new default calendar
    await updateCalendar(calendarId, { isDefault: true })
  }

  // Watch for profile changes
  watch(() => currentProfile.value, (newProfile) => {
    if (unsubscribe) {
      unsubscribe()
    }

    if (newProfile) {
      setupSubscription()
    }
  }, { immediate: true })

  // Cleanup on unmount
  onUnmounted(() => {
    if (unsubscribe) {
      unsubscribe()
    }
  })

  return {
    // State
    calendars: readonly(calendars),
    selectedCalendarId,
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Computed
    defaultCalendar,
    visibleCalendars,
    selectedCalendar,

    // Methods
    createCalendar,
    updateCalendar,
    deleteCalendar,
    toggleCalendarVisibility,
    setDefaultCalendar,
    setupSubscription,
  }
}
