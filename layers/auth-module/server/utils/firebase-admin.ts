/**
 * DEPRECATED: This file is deprecated and should not be used.
 * 
 * Please use one of the two approved Firebase approaches:
 * 
 * 1. Client-side: Use useDataApi() from composables/useDataApi.ts
 * 2. Server-side: Use useFirebaseServer() from server/firebase/init.ts
 * 
 * This stub is provided temporarily to prevent build errors while
 * files are migrated to the correct approach.
 */

import { useFirebaseServer } from '../firebase/init'

/**
 * @deprecated Use useFirebaseServer() from server/firebase/init.ts instead
 * This function is provided for backward compatibility only
 */
export function getFirestoreAdmin() {
  throw new Error(`
    getFirestoreAdmin() is deprecated!
    
    Please use one of the approved Firebase approaches:
    
    1. Server-side: Use useFirebaseServer(idToken) from server/firebase/init.ts
    2. Client-side: Use useDataApi() from composables/useDataApi.ts
    
    Example server-side usage:
    const session = await getUserSession(event)
    const idToken = session?.user?.token?.idToken
    const { firestore } = await useFirebaseServer(idToken)
  `)
}

/**
 * @deprecated This is a temporary compatibility export
 */
export default {
  getFirestoreAdmin,
}
