import { useFirebaseServer } from '../../firebase/init'
import { getUserSession } from '../../utils/session'

export default defineEventHandler(async (event) => {
  // Only available in development
  if (process.env.NODE_ENV !== 'development') {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not Found',
    })
  }

  try {
    const config = useRuntimeConfig()

    // Get session and Firebase instance using the proper server-side approach
    const session = await getUserSession(event)
    const idToken = session?.user?.token?.idToken || session?.user?.token?.accessToken
    const { firebaseApp } = await useFirebaseServer(idToken as string)

    // Check if emulator is configured by checking environment variables
    const isEmulatorConfigured = !!(
      process.env.FIRESTORE_EMULATOR_HOST ||
      process.env.FIREBASE_AUTH_EMULATOR_HOST ||
      config.public.firebase?.useEmulator
    )

    return {
      environment: process.env.NODE_ENV,
      firebaseConfig: {
        projectId: config.public.firebase.projectId,
        useEmulator: config.public.firebase.useEmulator,
      },
      serverSideConfig: {
        firestoreEmulatorHost: process.env.FIRESTORE_EMULATOR_HOST,
        authEmulatorHost: process.env.FIREBASE_AUTH_EMULATOR_HOST,
        isEmulatorConfigured,
      },
      environmentVariables: {
        FIREBASE_USE_EMULATOR: process.env.FIREBASE_USE_EMULATOR,
        NODE_ENV: process.env.NODE_ENV,
      },
    }
  }
  catch (error) {
    console.error('Firebase config debug error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get Firebase configuration',
    })
  }
})
