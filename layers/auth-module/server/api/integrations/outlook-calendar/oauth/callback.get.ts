import { OAuthService } from '../../../../services/oauth-base'
import { useFirebaseServer } from '../../../../firebase/init'
import { getUserSession } from '../../../../utils/session'

export default defineEventHandler(async (event) => {
  try {
    // Get session and Firebase instance using the proper server-side approach
    const session = await getUserSession(event)
    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    const config = useRuntimeConfig()
    const queryParams = getQuery(event)
    const { code, state, error: authError } = queryParams

    if (authError) {
      throw new Error(`Outlook Calendar OAuth error: ${authError}`)
    }

    if (!code || !state) {
      throw new Error('Missing authorization code or state')
    }

    // Verify state
    const storedState = getCookie(event, 'outlook-calendar_oauth_state')
    if (!storedState || storedState !== state) {
      throw new Error('Invalid OAuth state')
    }

    // Clear state cookie
    deleteCookie(event, 'outlook-calendar_oauth_state')

    // Parse state to get user info
    const stateData = JSON.parse(state as string)
    const { userId, workspaceId, returnUrl } = stateData

    if (!userId || !workspaceId) {
      throw new Error('Invalid state data')
    }

    // Exchange code for access token
    const redirectUri = `${getRequestURL(event).origin}/api/integrations/outlook-calendar/oauth/callback`
    const tokenData = await OAuthService.exchangeCodeForToken('outlook', code as string, redirectUri)

    // Get user profile from Microsoft Graph
    const profile = await OAuthService.getUserProfile('outlook', tokenData.accessToken)

    // Get user's calendars
    let calendars = []
    try {
      const calendarsResponse = await fetch(
        'https://graph.microsoft.com/v1.0/me/calendars',
        {
          headers: {
            Authorization: `Bearer ${tokenData.accessToken}`,
            Accept: 'application/json',
          },
        },
      )

      if (calendarsResponse.ok) {
        const calendarsData = await calendarsResponse.json()
        calendars = calendarsData.value || []
      }
    }
    catch (error) {
      console.error('Failed to fetch Outlook calendars:', error)
    }

    const primaryCalendar = calendars.find(cal => cal.isDefaultCalendar) || calendars[0]

    if (!primaryCalendar) {
      throw new Error('No calendar found')
    }

    // Create calendar integration document
    const integration = {
      userId,
      workspaceId,
      provider: 'outlook',
      credentials: {
        accessToken: tokenData.accessToken,
        refreshToken: tokenData.refreshToken,
        expiryDate: tokenData.expiresIn ? Date.now() + (tokenData.expiresIn * 1000) : Date.now() + 3600000,
        tokenType: tokenData.tokenType || 'Bearer',
      },
      calendarId: primaryCalendar.id,
      calendarName: primaryCalendar.name || 'Primary Calendar',
      isActive: true,
      syncStatus: {
        lastSync: new Date(),
        isActive: true,
        syncDirection: 'bidirectional',
        eventCount: 0,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Save to Firestore using Firebase client SDK
    const { collection, addDoc } = await import('firebase/firestore')
    await addDoc(collection(firestore, 'calendar_integrations'), integration)

    // Redirect back to the original page
    const baseUrl = config.public.oauth?.google?.baseUrl || 'http://localhost:3000'
    const redirectUrl = returnUrl || `${baseUrl}/user/integrations`

    return sendRedirect(event, `${redirectUrl}?connected=outlook-calendar`)
  }
  catch (error) {
    console.error('Outlook Calendar OAuth callback error:', error)

    const baseUrl = config.public.oauth?.google?.baseUrl || 'http://localhost:3000'
    const errorUrl = `${baseUrl}/user/integrations?error=${encodeURIComponent(error.message)}`
    return sendRedirect(event, errorUrl)
  }
})
