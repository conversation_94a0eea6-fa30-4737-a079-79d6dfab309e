import { google } from 'googleapis'
import { useFirebaseServer } from '../../../../firebase/init'
import { getUserSession } from '../../../../utils/session'

interface ListCalendarsQuery {
  maxResults?: number
  showDeleted?: boolean
  showHidden?: boolean
  minAccessRole?: 'freeBusyReader' | 'reader' | 'writer' | 'owner'
  pageToken?: string
  syncToken?: string
}

export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session || !session.isAuthenticated) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get Firebase instance with session context
    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    const queryParams = getQuery(event) as ListCalendarsQuery

    // Get user's Google Calendar integration using Firebase client SDK
    const { collection, query: buildQuery, where, limit, getDocs } = await import('firebase/firestore')

    const integrationQuery = buildQuery(
      collection(firestore, 'calendar_integrations'),
      where('userId', '==', session.user.id),
      where('workspaceId', '==', session.currentWorkspace?.id),
      where('provider', '==', 'google-calendar'),
      limit(1)
    )

    const integrationSnapshot = await getDocs(integrationQuery)

    if (integrationSnapshot.empty) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Google Calendar integration not found',
      })
    }

    const integration = integrationSnapshot.docs[0].data()

    // Setup Google Calendar API with token refresh
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
    )

    oauth2Client.setCredentials({
      access_token: integration.credentials.accessToken,
      refresh_token: integration.credentials.refreshToken,
    })

    // Handle token refresh
    oauth2Client.on('tokens', async (tokens) => {
      if (tokens.access_token) {
        await integrationQuery.docs[0].ref.update({
          'credentials.accessToken': tokens.access_token,
          'credentials.expiresAt': tokens.expiry_date
            ? new Date(tokens.expiry_date).toISOString()
            : null,
          'updatedAt': new Date().toISOString(),
        })
      }
    })

    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })

    // Build request parameters
    const requestParams: any = {
      maxResults: Math.min(queryParams.maxResults || 250, 250), // Google Calendar limit
      showDeleted: queryParams.showDeleted || false,
      showHidden: queryParams.showHidden || false,
    }

    if (queryParams.minAccessRole) {
      requestParams.minAccessRole = queryParams.minAccessRole
    }
    if (queryParams.pageToken) {
      requestParams.pageToken = queryParams.pageToken
    }
    if (queryParams.syncToken) {
      requestParams.syncToken = queryParams.syncToken
    }

    // Fetch calendars from Google Calendar
    const response = await calendar.calendarList.list(requestParams)

    // Transform calendars to match our internal format
    const calendars = response.data.items?.map(googleCalendar => ({
      id: googleCalendar.id,
      name: googleCalendar.summary || 'Untitled Calendar',
      description: googleCalendar.description || '',
      location: googleCalendar.location || '',
      timeZone: googleCalendar.timeZone || 'UTC',
      colorId: googleCalendar.colorId,
      backgroundColor: googleCalendar.backgroundColor,
      foregroundColor: googleCalendar.foregroundColor,
      accessRole: googleCalendar.accessRole,
      defaultReminders: googleCalendar.defaultReminders?.map(reminder => ({
        method: reminder.method,
        minutes: reminder.minutes,
      })) || [],
      notificationSettings: googleCalendar.notificationSettings
        ? {
            notifications: googleCalendar.notificationSettings.notifications?.map(notification => ({
              type: notification.type,
              method: notification.method,
            })) || [],
          }
        : null,
      primary: googleCalendar.primary || false,
      selected: googleCalendar.selected !== false, // default to true if not specified
      hidden: googleCalendar.hidden || false,
      deleted: googleCalendar.deleted || false,
      summaryOverride: googleCalendar.summaryOverride,
      conferenceProperties: googleCalendar.conferenceProperties
        ? {
            allowedConferenceSolutionTypes: googleCalendar.conferenceProperties.allowedConferenceSolutionTypes || [],
          }
        : null,
      etag: googleCalendar.etag,
    })) || []

    // Categorize calendars
    const categorizedCalendars = {
      primary: calendars.filter(cal => cal.primary),
      owned: calendars.filter(cal => cal.accessRole === 'owner' && !cal.primary),
      subscribed: calendars.filter(cal => ['reader', 'writer'].includes(cal.accessRole || '')),
      shared: calendars.filter(cal => cal.accessRole === 'freeBusyReader'),
      all: calendars,
    }

    // Get calendar statistics (optional)
    const stats = {
      total: calendars.length,
      primary: categorizedCalendars.primary.length,
      owned: categorizedCalendars.owned.length,
      subscribed: categorizedCalendars.subscribed.length,
      shared: categorizedCalendars.shared.length,
      hidden: calendars.filter(cal => cal.hidden).length,
      selected: calendars.filter(cal => cal.selected).length,
    }

    return {
      success: true,
      data: {
        calendars: categorizedCalendars,
        stats,
        pagination: {
          nextPageToken: response.data.nextPageToken,
          nextSyncToken: response.data.nextSyncToken,
        },
      },
    }
  }
  catch (error: any) {
    console.error('Google Calendar calendars list error:', error)

    // Handle specific Google API errors
    if (error.response?.status === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Google Calendar authorization expired. Please reconnect.',
      })
    }

    if (error.response?.status === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to access calendars',
      })
    }

    if (error.response?.status === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Calendars not found',
      })
    }

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to fetch calendars: ${error.message}`,
    })
  }
})
