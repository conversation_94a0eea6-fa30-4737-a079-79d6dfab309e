import { google } from 'googleapis'
import { useFirebaseServer } from '../../../firebase/init'
import { getUserSession } from '../../../utils/session'

export default defineEventHandler(async (event) => {
  try {
    // Get session and Firebase instance using the proper server-side approach
    const session = await getUserSession(event)
    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    const body = await readBody(event)
    const { integrationId } = body

    if (!integrationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Integration ID is required',
      })
    }

    // Get integration document using Firebase client SDK
    const { doc, getDoc } = await import('firebase/firestore')
    const integrationDoc = await getDoc(doc(firestore, 'calendar_integrations', integrationId))

    if (!integrationDoc.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Integration not found',
      })
    }

    const integration = integrationDoc.data()

    // Revoke Google tokens
    try {
      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
      )

      oauth2Client.setCredentials({
        access_token: integration.credentials.accessToken,
        refresh_token: integration.credentials.refreshToken,
      })

      await oauth2Client.revokeCredentials()
    }
    catch (revokeError) {
      console.warn('Failed to revoke Google tokens:', revokeError)
      // Continue with local cleanup even if revocation fails
    }

    // Delete integration document using Firebase client SDK
    const { deleteDoc } = await import('firebase/firestore')
    await deleteDoc(doc(firestore, 'calendar_integrations', integrationId))

    return {
      success: true,
      message: 'Google Calendar integration disconnected successfully',
    }
  }
  catch (error) {
    console.error('Google Calendar disconnect error:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to disconnect Google Calendar: ${error.message}`,
    })
  }
})
