import { Timestamp } from 'firebase/firestore'
import { google } from 'googleapis'
import { useFirebaseServer } from '../../../../firebase/init'
import { getUserSession } from '../../../../utils/session'

interface DeleteEventQuery {
  calendarId?: string
  sendUpdates?: 'all' | 'externalOnly' | 'none'
}

export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session || !session.isAuthenticated) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    const eventId = getRouterParam(event, 'id')
    if (!eventId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Event ID is required',
      })
    }

    const query = getQuery(event) as DeleteEventQuery

    // Get Firebase instance with session context
    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    // Get user's Google Calendar integration using Firebase client SDK
    const { collection, query: buildQuery, where, limit, getDocs } = await import('firebase/firestore')

    const integrationQuery = buildQuery(
      collection(firestore, 'calendar_integrations'),
      where('userId', '==', session.user.id),
      where('workspaceId', '==', session.currentWorkspace?.id),
      where('provider', '==', 'google-calendar'),
      limit(1)
    )

    const integrationSnapshot = await getDocs(integrationQuery)

    if (integrationSnapshot.empty) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Google Calendar integration not found',
      })
    }

    const integration = integrationSnapshot.docs[0].data()

    // Setup Google Calendar API with token refresh
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
    )

    oauth2Client.setCredentials({
      access_token: integration.credentials.accessToken,
      refresh_token: integration.credentials.refreshToken,
    })

    // Handle token refresh
    oauth2Client.on('tokens', async (tokens) => {
      if (tokens.access_token) {
        await integrationQuery.docs[0].ref.update({
          'credentials.accessToken': tokens.access_token,
          'credentials.expiresAt': tokens.expiry_date
            ? new Date(tokens.expiry_date).toISOString()
            : null,
          'updatedAt': new Date().toISOString(),
        })
      }
    })

    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })

    const calendarId = query.calendarId || integration.calendarId || 'primary'

    // Get the event details before deletion (for logging and local cleanup)
    let eventDetails = null
    try {
      const eventResponse = await calendar.events.get({
        calendarId,
        eventId,
      })
      eventDetails = eventResponse.data
    }
    catch (error) {
      console.log('Could not fetch event details before deletion:', error)
    }

    // Delete the event from Google Calendar
    await calendar.events.delete({
      calendarId,
      eventId,
      sendUpdates: query.sendUpdates || 'all',
    })

    // Also delete from our local calendar_events collection if it exists
    let localEventDeleted = false
    try {
      const localEventQuery = buildQuery(
        collection(firestore, 'calendar_events'),
        where('googleEventId', '==', eventId),
        where('workspaceId', '==', session.currentWorkspace?.id),
        limit(1)
      )

      const localEventSnapshot = await getDocs(localEventQuery)

      if (!localEventSnapshot.empty) {
        const localEventDoc = localEventSnapshot.docs[0]

        // Instead of hard deleting, mark as deleted (soft delete)
        const { updateDoc } = await import('firebase/firestore')
        await updateDoc(localEventDoc.ref, {
          status: 'cancelled',
          deletedAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
          syncedAt: Timestamp.now(),
        })

        localEventDeleted = true
      }
    }
    catch (error) {
      console.log('Local event deletion failed, continuing with Google deletion:', error)
    }

    // Log the deletion for audit purposes
    try {
      await db.collection('calendar_event_deletions').add({
        eventId,
        googleEventId: eventId,
        calendarId,
        userId: session.user.id,
        workspaceId: session.currentWorkspace?.id,
        provider: 'google-calendar',
        eventDetails: eventDetails
          ? {
              title: eventDetails.summary,
              startDate: eventDetails.start?.dateTime || eventDetails.start?.date,
              endDate: eventDetails.end?.dateTime || eventDetails.end?.date,
              creator: eventDetails.creator,
              organizer: eventDetails.organizer,
            }
          : null,
        deletedAt: Timestamp.now(),
        deletedBy: session.user.id,
        sendUpdates: query.sendUpdates || 'all',
        localEventDeleted,
      })
    }
    catch (error) {
      console.log('Failed to log event deletion:', error)
    }

    return {
      success: true,
      data: {
        eventId,
        googleEventId: eventId,
        calendarId,
        deleted: true,
        localEventDeleted,
        eventDetails: eventDetails
          ? {
              title: eventDetails.summary,
              startDate: eventDetails.start?.dateTime || eventDetails.start?.date,
              endDate: eventDetails.end?.dateTime || eventDetails.end?.date,
            }
          : null,
      },
      message: 'Calendar event deleted successfully',
    }
  }
  catch (error: any) {
    console.error('Google Calendar event deletion error:', error)

    // Handle specific Google API errors
    if (error.response?.status === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Google Calendar authorization expired. Please reconnect.',
      })
    }

    if (error.response?.status === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to delete calendar events',
      })
    }

    if (error.response?.status === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Calendar event not found',
      })
    }

    if (error.response?.status === 410) {
      // Event was already deleted
      return {
        success: true,
        data: {
          eventId: getRouterParam(event, 'id'),
          googleEventId: getRouterParam(event, 'id'),
          calendarId: query.calendarId || 'primary',
          deleted: true,
          alreadyDeleted: true,
        },
        message: 'Calendar event was already deleted',
      }
    }

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to delete calendar event: ${error.message}`,
    })
  }
})
