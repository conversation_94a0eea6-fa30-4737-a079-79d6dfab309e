import { Timestamp } from 'firebase/firestore'
import { google } from 'googleapis'
import { useFirebaseServer } from '../../../../firebase/init'
import { getUserSession } from '../../../../utils/session'

interface UpdateEventData {
  calendarId?: string
  title?: string
  description?: string
  location?: string
  startDate?: string
  endDate?: string
  allDay?: boolean
  timeZone?: string
  attendees?: Array<{
    email: string
    name?: string
    optional?: boolean
    responseStatus?: string
  }>
  recurrence?: string[]
  reminders?: {
    useDefault?: boolean
    overrides?: Array<{
      method: 'email' | 'popup'
      minutes: number
    }>
  }
  visibility?: 'default' | 'public' | 'private'
  status?: 'confirmed' | 'tentative' | 'cancelled'
  conferenceData?: {
    createRequest?: {
      requestId: string
      conferenceSolutionKey: {
        type: string
      }
    }
  }
  colorId?: string
  guestsCanInviteOthers?: boolean
  guestsCanModify?: boolean
  guestsCanSeeOtherGuests?: boolean
  sendUpdates?: 'all' | 'externalOnly' | 'none'
  transparency?: 'opaque' | 'transparent'
  sequence?: number
}

export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session || !session.isAuthenticated) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    const eventId = getRouterParam(event, 'id')
    if (!eventId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Event ID is required',
      })
    }

    const body = await readBody(event) as UpdateEventData

    // Get Firebase instance with session context
    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    // Get user's Google Calendar integration using Firebase client SDK
    const { collection, query: buildQuery, where, limit, getDocs } = await import('firebase/firestore')

    const integrationQuery = buildQuery(
      collection(firestore, 'calendar_integrations'),
      where('userId', '==', session.user.id),
      where('workspaceId', '==', session.currentWorkspace?.id),
      where('provider', '==', 'google-calendar'),
      limit(1)
    )

    const integrationSnapshot = await getDocs(integrationQuery)

    if (integrationSnapshot.empty) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Google Calendar integration not found',
      })
    }

    const integration = integrationSnapshot.docs[0].data()

    // Setup Google Calendar API with token refresh
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
    )

    oauth2Client.setCredentials({
      access_token: integration.credentials.accessToken,
      refresh_token: integration.credentials.refreshToken,
    })

    // Handle token refresh
    oauth2Client.on('tokens', async (tokens) => {
      if (tokens.access_token) {
        await integrationSnapshot.docs[0].ref.update({
          'credentials.accessToken': tokens.access_token,
          'credentials.expiresAt': tokens.expiry_date
            ? new Date(tokens.expiry_date).toISOString()
            : null,
          'updatedAt': new Date().toISOString(),
        })
      }
    })

    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })

    const calendarId = body.calendarId || integration.calendarId || 'primary'
    const timeZone = body.timeZone || integration.timeZone || 'America/New_York'

    // First, get the current event to avoid overwriting fields not being updated
    const currentEvent = await calendar.events.get({
      calendarId,
      eventId,
    })

    if (!currentEvent.data) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Event not found',
      })
    }

    // Build the updated event object, preserving existing values for fields not being updated
    const updatedEvent: any = {
      ...currentEvent.data,
    }

    // Update only the fields that are provided
    if (body.title !== undefined) {
      updatedEvent.summary = body.title
    }
    if (body.description !== undefined) {
      updatedEvent.description = body.description
    }
    if (body.location !== undefined) {
      updatedEvent.location = body.location
    }
    if (body.status !== undefined) {
      updatedEvent.status = body.status
    }
    if (body.visibility !== undefined) {
      updatedEvent.visibility = body.visibility
    }
    if (body.transparency !== undefined) {
      updatedEvent.transparency = body.transparency
    }
    if (body.colorId !== undefined) {
      updatedEvent.colorId = body.colorId
    }
    if (body.sequence !== undefined) {
      updatedEvent.sequence = body.sequence
    }

    // Handle date/time updates
    if (body.startDate || body.endDate) {
      const startDate = body.startDate ? new Date(body.startDate) : new Date(currentEvent.data.start?.dateTime || currentEvent.data.start?.date)
      const endDate = body.endDate ? new Date(body.endDate) : new Date(currentEvent.data.end?.dateTime || currentEvent.data.end?.date)

      // Validate dates
      if (body.startDate && isNaN(startDate.getTime())) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid start date format. Use ISO 8601 format.',
        })
      }

      if (body.endDate && isNaN(endDate.getTime())) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid end date format. Use ISO 8601 format.',
        })
      }

      if (startDate >= endDate) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Start date must be before end date',
        })
      }

      // Update date/time formatting
      const isAllDay = body.allDay !== undefined ? body.allDay : !!currentEvent.data.start?.date

      if (isAllDay) {
        updatedEvent.start = {
          date: startDate.toISOString().split('T')[0],
          timeZone,
        }
        updatedEvent.end = {
          date: endDate.toISOString().split('T')[0],
          timeZone,
        }
      }
      else {
        updatedEvent.start = {
          dateTime: startDate.toISOString(),
          timeZone,
        }
        updatedEvent.end = {
          dateTime: endDate.toISOString(),
          timeZone,
        }
      }
    }

    // Update attendees
    if (body.attendees !== undefined) {
      updatedEvent.attendees = body.attendees.map(attendee => ({
        email: attendee.email,
        displayName: attendee.name,
        optional: attendee.optional || false,
        responseStatus: attendee.responseStatus || 'needsAction',
      }))
    }

    // Update recurrence rules
    if (body.recurrence !== undefined) {
      updatedEvent.recurrence = body.recurrence.length > 0 ? body.recurrence : null
    }

    // Update reminders
    if (body.reminders !== undefined) {
      updatedEvent.reminders = {
        useDefault: body.reminders.useDefault !== false,
        overrides: body.reminders.overrides || [],
      }
    }

    // Update conference data (for Google Meet)
    if (body.conferenceData !== undefined) {
      updatedEvent.conferenceData = body.conferenceData
    }

    // Update guest permissions
    if (body.guestsCanInviteOthers !== undefined) {
      updatedEvent.guestsCanInviteOthers = body.guestsCanInviteOthers
    }
    if (body.guestsCanModify !== undefined) {
      updatedEvent.guestsCanModify = body.guestsCanModify
    }
    if (body.guestsCanSeeOtherGuests !== undefined) {
      updatedEvent.guestsCanSeeOtherGuests = body.guestsCanSeeOtherGuests
    }

    // Update the event
    const response = await calendar.events.update({
      calendarId,
      eventId,
      requestBody: updatedEvent,
      conferenceDataVersion: body.conferenceData ? 1 : 0,
      sendUpdates: body.sendUpdates || 'all',
    })

    const updatedGoogleEvent = response.data

    // Also update in our local calendar_events collection if it exists
    try {
      const localEventQuery = buildQuery(
        collection(firestore, 'calendar_events'),
        where('googleEventId', '==', eventId),
        where('workspaceId', '==', session.currentWorkspace?.id),
        limit(1)
      )

      const localEventSnapshot = await getDocs(localEventQuery)

      if (!localEventSnapshot.empty) {
        const localEventDoc = localEventSnapshot.docs[0]
        const localUpdateData: any = {
          updatedAt: Timestamp.now(),
          syncedAt: Timestamp.now(),
        }

        if (body.title !== undefined) {
          localUpdateData.title = body.title
        }
        if (body.description !== undefined) {
          localUpdateData.description = body.description
        }
        if (body.location !== undefined) {
          localUpdateData.location = body.location
        }
        if (body.status !== undefined) {
          localUpdateData.status = body.status
        }
        if (body.visibility !== undefined) {
          localUpdateData.visibility = body.visibility
        }
        if (body.startDate !== undefined) {
          localUpdateData.startDate = Timestamp.fromDate(new Date(body.startDate))
        }
        if (body.endDate !== undefined) {
          localUpdateData.endDate = Timestamp.fromDate(new Date(body.endDate))
        }
        if (body.allDay !== undefined) {
          localUpdateData.allDay = body.allDay
        }
        if (body.attendees !== undefined) {
          localUpdateData.participants = body.attendees.map(attendee => ({
            id: Math.random().toString(36).substring(7),
            name: attendee.name || attendee.email,
            email: attendee.email,
            photo: '/img/avatars/default.svg',
            optional: attendee.optional || false,
          }))
        }

        const { updateDoc } = await import('firebase/firestore')
        await updateDoc(localEventDoc.ref, localUpdateData)
      }
    }
    catch (error) {
      console.log('Local event update failed, continuing with Google update:', error)
    }

    // Transform the response to match our internal format
    const transformedEvent = {
      id: updatedGoogleEvent.id,
      googleEventId: updatedGoogleEvent.id,
      title: updatedGoogleEvent.summary || 'Untitled Event',
      description: updatedGoogleEvent.description || '',
      location: updatedGoogleEvent.location || '',
      startDate: updatedGoogleEvent.start?.dateTime || updatedGoogleEvent.start?.date,
      endDate: updatedGoogleEvent.end?.dateTime || updatedGoogleEvent.end?.date,
      allDay: !!updatedGoogleEvent.start?.date,
      status: updatedGoogleEvent.status || 'confirmed',
      visibility: updatedGoogleEvent.visibility || 'default',
      attendees: updatedGoogleEvent.attendees?.map(attendee => ({
        email: attendee.email,
        name: attendee.displayName || attendee.email,
        responseStatus: attendee.responseStatus,
        optional: attendee.optional,
      })) || [],
      creator: updatedGoogleEvent.creator
        ? {
            email: updatedGoogleEvent.creator.email,
            name: updatedGoogleEvent.creator.displayName || updatedGoogleEvent.creator.email,
          }
        : null,
      organizer: updatedGoogleEvent.organizer
        ? {
            email: updatedGoogleEvent.organizer.email,
            name: updatedGoogleEvent.organizer.displayName || updatedGoogleEvent.organizer.email,
          }
        : null,
      hangoutLink: updatedGoogleEvent.hangoutLink,
      conferenceData: updatedGoogleEvent.conferenceData,
      htmlLink: updatedGoogleEvent.htmlLink,
      created: updatedGoogleEvent.created,
      updated: updatedGoogleEvent.updated,
      etag: updatedGoogleEvent.etag,
      sequence: updatedGoogleEvent.sequence,
    }

    return {
      success: true,
      data: transformedEvent,
      message: 'Calendar event updated successfully',
    }
  }
  catch (error: any) {
    console.error('Google Calendar event update error:', error)

    // Handle specific Google API errors
    if (error.response?.status === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Google Calendar authorization expired. Please reconnect.',
      })
    }

    if (error.response?.status === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to update calendar events',
      })
    }

    if (error.response?.status === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Calendar event not found',
      })
    }

    if (error.response?.status === 409) {
      throw createError({
        statusCode: 409,
        statusMessage: 'Event conflict or version mismatch',
      })
    }

    if (error.response?.status === 412) {
      throw createError({
        statusCode: 412,
        statusMessage: 'Precondition failed - event may have been modified by another client',
      })
    }

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to update calendar event: ${error.message}`,
    })
  }
})
