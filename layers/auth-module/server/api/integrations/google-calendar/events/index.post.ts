import { Timestamp } from 'firebase/firestore'
import { google } from 'googleapis'
import { useFirebaseServer } from '../../../../firebase/init'
import { getUserSession } from '../../../../utils/session'

interface CreateEventData {
  calendarId?: string
  title: string
  description?: string
  location?: string
  startDate: string
  endDate: string
  allDay?: boolean
  timeZone?: string
  attendees?: Array<{
    email: string
    name?: string
    optional?: boolean
  }>
  recurrence?: string[]
  reminders?: {
    useDefault?: boolean
    overrides?: Array<{
      method: 'email' | 'popup'
      minutes: number
    }>
  }
  visibility?: 'default' | 'public' | 'private'
  status?: 'confirmed' | 'tentative' | 'cancelled'
  conferenceData?: {
    createRequest?: {
      requestId: string
      conferenceSolutionKey: {
        type: string
      }
    }
  }
  colorId?: string
  guestsCanInviteOthers?: boolean
  guestsCanModify?: boolean
  guestsCanSeeOtherGuests?: boolean
  sendUpdates?: 'all' | 'externalOnly' | 'none'
}

export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session || !session.isAuthenticated) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    const body = await readBody(event) as CreateEventData

    // Validate required fields
    if (!body.title || !body.startDate || !body.endDate) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Title, start date, and end date are required',
      })
    }

    // Validate date format
    const startDate = new Date(body.startDate)
    const endDate = new Date(body.endDate)

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid date format. Use ISO 8601 format.',
      })
    }

    if (startDate >= endDate) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Start date must be before end date',
      })
    }

    // Get Firebase instance with session context
    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    // Get user's Google Calendar integration using Firebase client SDK
    const { collection, query: buildQuery, where, limit, getDocs } = await import('firebase/firestore')

    const integrationQuery = buildQuery(
      collection(firestore, 'calendar_integrations'),
      where('userId', '==', session.user.id),
      where('workspaceId', '==', session.currentWorkspace?.id),
      where('provider', '==', 'google-calendar'),
      limit(1)
    )

    const integrationSnapshot = await getDocs(integrationQuery)

    if (integrationSnapshot.empty) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Google Calendar integration not found',
      })
    }

    const integration = integrationSnapshot.docs[0].data()

    // Setup Google Calendar API with token refresh
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
    )

    oauth2Client.setCredentials({
      access_token: integration.credentials.accessToken,
      refresh_token: integration.credentials.refreshToken,
    })

    // Handle token refresh
    oauth2Client.on('tokens', async (tokens) => {
      if (tokens.access_token) {
        await integrationSnapshot.docs[0].ref.update({
          'credentials.accessToken': tokens.access_token,
          'credentials.expiresAt': tokens.expiry_date
            ? new Date(tokens.expiry_date).toISOString()
            : null,
          'updatedAt': new Date().toISOString(),
        })
      }
    })

    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })

    const calendarId = body.calendarId || integration.calendarId || 'primary'
    const timeZone = body.timeZone || integration.timeZone || 'America/New_York'

    // Build Google Calendar event object
    const googleEvent: any = {
      summary: body.title,
      description: body.description || '',
      location: body.location || '',
      status: body.status || 'confirmed',
      visibility: body.visibility || 'default',
    }

    // Handle date/time formatting
    if (body.allDay) {
      googleEvent.start = {
        date: startDate.toISOString().split('T')[0],
        timeZone,
      }
      googleEvent.end = {
        date: endDate.toISOString().split('T')[0],
        timeZone,
      }
    }
    else {
      googleEvent.start = {
        dateTime: startDate.toISOString(),
        timeZone,
      }
      googleEvent.end = {
        dateTime: endDate.toISOString(),
        timeZone,
      }
    }

    // Add attendees
    if (body.attendees && body.attendees.length > 0) {
      googleEvent.attendees = body.attendees.map(attendee => ({
        email: attendee.email,
        displayName: attendee.name,
        optional: attendee.optional || false,
      }))
    }

    // Add recurrence rules
    if (body.recurrence && body.recurrence.length > 0) {
      googleEvent.recurrence = body.recurrence
    }

    // Add reminders
    if (body.reminders) {
      googleEvent.reminders = {
        useDefault: body.reminders.useDefault !== false,
        overrides: body.reminders.overrides || [],
      }
    }

    // Add conference data (for Google Meet)
    if (body.conferenceData) {
      googleEvent.conferenceData = body.conferenceData
    }

    // Add color
    if (body.colorId) {
      googleEvent.colorId = body.colorId
    }

    // Add guest permissions
    if (body.guestsCanInviteOthers !== undefined) {
      googleEvent.guestsCanInviteOthers = body.guestsCanInviteOthers
    }
    if (body.guestsCanModify !== undefined) {
      googleEvent.guestsCanModify = body.guestsCanModify
    }
    if (body.guestsCanSeeOtherGuests !== undefined) {
      googleEvent.guestsCanSeeOtherGuests = body.guestsCanSeeOtherGuests
    }

    // Create the event
    const response = await calendar.events.insert({
      calendarId,
      requestBody: googleEvent,
      conferenceDataVersion: body.conferenceData ? 1 : 0,
      sendUpdates: body.sendUpdates || 'all',
    })

    const createdEvent = response.data

    // Also store in our local calendar_events collection for sync
    const localEventData = {
      title: body.title,
      description: body.description || '',
      location: body.location || '',
      startDate: Timestamp.fromDate(startDate),
      endDate: Timestamp.fromDate(endDate),
      duration: Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60)),
      allDay: body.allDay || false,
      category: 'personal',
      status: body.status || 'confirmed',
      visibility: body.visibility || 'default',
      userId: session.user.id,
      workspaceId: session.currentWorkspace?.id,
      participants: body.attendees?.map(attendee => ({
        id: Math.random().toString(36).substring(7),
        name: attendee.name || attendee.email,
        email: attendee.email,
        photo: '/img/avatars/default.svg',
        optional: attendee.optional || false,
      })) || [],
      googleEventId: createdEvent.id,
      googleCalendarId: calendarId,
      syncedAt: Timestamp.now(),
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    }

    // Save to local database using Firebase client SDK
    const { addDoc } = await import('firebase/firestore')
    const localEventRef = await addDoc(collection(firestore, 'calendar_events'), localEventData)

    // Transform the response to match our internal format
    const transformedEvent = {
      id: createdEvent.id,
      localId: localEventRef.id,
      googleEventId: createdEvent.id,
      title: createdEvent.summary || 'Untitled Event',
      description: createdEvent.description || '',
      location: createdEvent.location || '',
      startDate: createdEvent.start?.dateTime || createdEvent.start?.date,
      endDate: createdEvent.end?.dateTime || createdEvent.end?.date,
      allDay: !!createdEvent.start?.date,
      status: createdEvent.status || 'confirmed',
      visibility: createdEvent.visibility || 'default',
      attendees: createdEvent.attendees?.map(attendee => ({
        email: attendee.email,
        name: attendee.displayName || attendee.email,
        responseStatus: attendee.responseStatus,
        optional: attendee.optional,
      })) || [],
      creator: createdEvent.creator
        ? {
            email: createdEvent.creator.email,
            name: createdEvent.creator.displayName || createdEvent.creator.email,
          }
        : null,
      organizer: createdEvent.organizer
        ? {
            email: createdEvent.organizer.email,
            name: createdEvent.organizer.displayName || createdEvent.organizer.email,
          }
        : null,
      hangoutLink: createdEvent.hangoutLink,
      conferenceData: createdEvent.conferenceData,
      htmlLink: createdEvent.htmlLink,
      created: createdEvent.created,
      updated: createdEvent.updated,
      etag: createdEvent.etag,
    }

    return {
      success: true,
      data: transformedEvent,
      message: 'Calendar event created successfully',
    }
  }
  catch (error: any) {
    console.error('Google Calendar event creation error:', error)

    // Handle specific Google API errors
    if (error.response?.status === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Google Calendar authorization expired. Please reconnect.',
      })
    }

    if (error.response?.status === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to create calendar events',
      })
    }

    if (error.response?.status === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Calendar not found',
      })
    }

    if (error.response?.status === 409) {
      throw createError({
        statusCode: 409,
        statusMessage: 'Event conflict or duplicate event',
      })
    }

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to create calendar event: ${error.message}`,
    })
  }
})
