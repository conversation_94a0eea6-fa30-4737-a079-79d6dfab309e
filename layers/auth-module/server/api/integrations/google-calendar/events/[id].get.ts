import { google } from 'googleapis'
import { useFirebaseServer } from '../../../../firebase/init'
import { getUserSession } from '../../../../utils/session'

interface GetEventQuery {
  calendarId?: string
  maxAttendees?: number
  timeZone?: string
}

export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session || !session.isAuthenticated) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    const eventId = getRouterParam(event, 'id')
    if (!eventId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Event ID is required',
      })
    }

    // Get Firebase instance with session context
    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    const queryParams = getQuery(event) as GetEventQuery

    // Get user's Google Calendar integration using Firebase client SDK
    const { collection, query: buildQuery, where, limit, getDocs } = await import('firebase/firestore')

    const integrationQuery = buildQuery(
      collection(firestore, 'calendar_integrations'),
      where('userId', '==', session.user.id),
      where('workspaceId', '==', session.currentWorkspace?.id),
      where('provider', '==', 'google-calendar'),
      limit(1)
    )

    const integrationSnapshot = await getDocs(integrationQuery)

    if (integrationSnapshot.empty) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Google Calendar integration not found',
      })
    }

    const integration = integrationSnapshot.docs[0].data()

    // Setup Google Calendar API with token refresh
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
    )

    oauth2Client.setCredentials({
      access_token: integration.credentials.accessToken,
      refresh_token: integration.credentials.refreshToken,
    })

    // Handle token refresh
    oauth2Client.on('tokens', async (tokens) => {
      if (tokens.access_token) {
        await integrationSnapshot.docs[0].ref.update({
          'credentials.accessToken': tokens.access_token,
          'credentials.expiresAt': tokens.expiry_date
            ? new Date(tokens.expiry_date).toISOString()
            : null,
          'updatedAt': new Date().toISOString(),
        })
      }
    })

    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })

    const calendarId = queryParams.calendarId || integration.calendarId || 'primary'

    // Build request parameters
    const requestParams: any = {
      calendarId,
      eventId,
    }

    if (queryParams.maxAttendees) {
      requestParams.maxAttendees = Math.min(queryParams.maxAttendees, 1000) // Google limit
    }

    if (queryParams.timeZone) {
      requestParams.timeZone = queryParams.timeZone
    }

    // Fetch the specific event from Google Calendar
    const response = await calendar.events.get(requestParams)
    const googleEvent = response.data

    // Check if we have a local copy of this event
    let localEvent = null
    try {
      const localEventQuery = buildQuery(
        collection(firestore, 'calendar_events'),
        where('googleEventId', '==', eventId),
        where('workspaceId', '==', session.currentWorkspace?.id),
        limit(1)
      )

      const localEventSnapshot = await getDocs(localEventQuery)

      if (!localEventSnapshot.empty) {
        localEvent = {
          id: localEventSnapshot.docs[0].id,
          ...localEventSnapshot.docs[0].data(),
        }
      }
    }
    catch (error) {
      console.log('No local event found, using Google data only')
    }

    // Transform the event to match our internal format
    const transformedEvent = {
      id: googleEvent.id,
      localId: localEvent?.id || null,
      googleEventId: googleEvent.id,
      title: googleEvent.summary || 'Untitled Event',
      description: googleEvent.description || '',
      location: googleEvent.location || '',
      startDate: googleEvent.start?.dateTime || googleEvent.start?.date,
      endDate: googleEvent.end?.dateTime || googleEvent.end?.date,
      allDay: !!googleEvent.start?.date, // If date (not dateTime), it's all-day
      status: googleEvent.status || 'confirmed',
      visibility: googleEvent.visibility || 'default',
      attendees: googleEvent.attendees?.map(attendee => ({
        email: attendee.email,
        name: attendee.displayName || attendee.email,
        responseStatus: attendee.responseStatus,
        optional: attendee.optional,
        resource: attendee.resource,
        additionalGuests: attendee.additionalGuests,
        comment: attendee.comment,
      })) || [],
      creator: googleEvent.creator
        ? {
            email: googleEvent.creator.email,
            name: googleEvent.creator.displayName || googleEvent.creator.email,
            self: googleEvent.creator.self,
          }
        : null,
      organizer: googleEvent.organizer
        ? {
            email: googleEvent.organizer.email,
            name: googleEvent.organizer.displayName || googleEvent.organizer.email,
            self: googleEvent.organizer.self,
          }
        : null,
      recurringEventId: googleEvent.recurringEventId,
      originalStartTime: googleEvent.originalStartTime,
      transparency: googleEvent.transparency,
      hangoutLink: googleEvent.hangoutLink,
      conferenceData: googleEvent.conferenceData,
      htmlLink: googleEvent.htmlLink,
      created: googleEvent.created,
      updated: googleEvent.updated,
      etag: googleEvent.etag,
      colorId: googleEvent.colorId,
      sequence: googleEvent.sequence,
      reminders: googleEvent.reminders,
      attachments: googleEvent.attachments?.map(attachment => ({
        fileId: attachment.fileId,
        fileUrl: attachment.fileUrl,
        iconLink: attachment.iconLink,
        mimeType: attachment.mimeType,
        title: attachment.title,
      })) || [],
      recurrence: googleEvent.recurrence,
      guestsCanInviteOthers: googleEvent.guestsCanInviteOthers,
      guestsCanModify: googleEvent.guestsCanModify,
      guestsCanSeeOtherGuests: googleEvent.guestsCanSeeOtherGuests,
      privateCopy: googleEvent.privateCopy,
      locked: googleEvent.locked,
      source: googleEvent.source,
      workingLocationProperties: googleEvent.workingLocationProperties,
      outOfOfficeProperties: googleEvent.outOfOfficeProperties,
      focusTimeProperties: googleEvent.focusTimeProperties,
      // Include local event data if available
      localData: localEvent
        ? {
            category: localEvent.category,
            participants: localEvent.participants,
            syncedAt: localEvent.syncedAt,
          }
        : null,
    }

    return {
      success: true,
      data: transformedEvent,
    }
  }
  catch (error: any) {
    console.error('Google Calendar event fetch error:', error)

    // Handle specific Google API errors
    if (error.response?.status === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Google Calendar authorization expired. Please reconnect.',
      })
    }

    if (error.response?.status === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to access calendar events',
      })
    }

    if (error.response?.status === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Calendar event not found',
      })
    }

    if (error.response?.status === 410) {
      throw createError({
        statusCode: 410,
        statusMessage: 'Calendar event has been deleted',
      })
    }

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to fetch calendar event: ${error.message}`,
    })
  }
})
