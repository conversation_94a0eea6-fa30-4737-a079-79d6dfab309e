import { google } from 'googleapis'
import { useFirebaseServer } from '../../../../firebase/init'
import { getUserSession } from '../../../../utils/session'

interface ListEventsQuery {
  calendarId?: string
  timeMin?: string
  timeMax?: string
  maxResults?: number
  singleEvents?: boolean
  orderBy?: 'startTime' | 'updated'
  showDeleted?: boolean
  syncToken?: string
  pageToken?: string
}

export default defineEventHandler(async (event) => {
  try {
    // Check authentication
    const session = await getUserSession(event)
    if (!session || !session.isAuthenticated) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    // Get Firebase instance with session context
    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    const queryParams = getQuery(event) as ListEventsQuery

    // Get user's Google Calendar integration using Firebase client SDK
    const { collection, query: buildQuery, where, limit, getDocs } = await import('firebase/firestore')

    const integrationQuery = buildQuery(
      collection(firestore, 'calendar_integrations'),
      where('userId', '==', session.user.id),
      where('workspaceId', '==', session.currentWorkspace?.id),
      where('provider', '==', 'google-calendar'),
      limit(1)
    )

    const integrationSnapshot = await getDocs(integrationQuery)

    if (integrationSnapshot.empty) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Google Calendar integration not found',
      })
    }

    const integration = integrationSnapshot.docs[0].data()

    // Setup Google Calendar API with token refresh
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
    )

    oauth2Client.setCredentials({
      access_token: integration.credentials.accessToken,
      refresh_token: integration.credentials.refreshToken,
    })

    // Handle token refresh
    oauth2Client.on('tokens', async (tokens) => {
      if (tokens.access_token) {
        await integrationSnapshot.docs[0].ref.update({
          'credentials.accessToken': tokens.access_token,
          'credentials.expiresAt': tokens.expiry_date
            ? new Date(tokens.expiry_date).toISOString()
            : null,
          'updatedAt': new Date().toISOString(),
        })
      }
    })

    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })

    // Set default query parameters
    const calendarId = queryParams.calendarId || integration.calendarId || 'primary'
    const timeMin = queryParams.timeMin || new Date().toISOString()
    const timeMax = queryParams.timeMax
    const maxResults = Math.min(queryParams.maxResults || 250, 2500) // Google Calendar limit
    const singleEvents = queryParams.singleEvents !== false // default to true
    const orderBy = queryParams.orderBy || 'startTime'
    const showDeleted = queryParams.showDeleted || false
    const syncToken = queryParams.syncToken
    const pageToken = queryParams.pageToken

    // Build request parameters
    const requestParams: any = {
      calendarId,
      timeMin,
      maxResults,
      singleEvents,
      showDeleted,
    }

    if (timeMax)
      requestParams.timeMax = timeMax
    if (singleEvents && orderBy)
      requestParams.orderBy = orderBy
    if (syncToken)
      requestParams.syncToken = syncToken
    if (pageToken)
      requestParams.pageToken = pageToken

    // Fetch events from Google Calendar
    const response = await calendar.events.list(requestParams)

    // Transform events to match our internal format
    const events = response.data.items?.map(googleEvent => ({
      id: googleEvent.id,
      googleEventId: googleEvent.id,
      title: googleEvent.summary || 'Untitled Event',
      description: googleEvent.description || '',
      location: googleEvent.location || '',
      startDate: googleEvent.start?.dateTime || googleEvent.start?.date,
      endDate: googleEvent.end?.dateTime || googleEvent.end?.date,
      allDay: !!googleEvent.start?.date, // If date (not dateTime), it's all-day
      status: googleEvent.status || 'confirmed',
      visibility: googleEvent.visibility || 'default',
      attendees: googleEvent.attendees?.map(attendee => ({
        email: attendee.email,
        name: attendee.displayName || attendee.email,
        responseStatus: attendee.responseStatus,
        optional: attendee.optional,
      })) || [],
      creator: googleEvent.creator
        ? {
            email: googleEvent.creator.email,
            name: googleEvent.creator.displayName || googleEvent.creator.email,
          }
        : null,
      organizer: googleEvent.organizer
        ? {
            email: googleEvent.organizer.email,
            name: googleEvent.organizer.displayName || googleEvent.organizer.email,
          }
        : null,
      recurringEventId: googleEvent.recurringEventId,
      originalStartTime: googleEvent.originalStartTime,
      hangoutLink: googleEvent.hangoutLink,
      conferenceData: googleEvent.conferenceData,
      htmlLink: googleEvent.htmlLink,
      created: googleEvent.created,
      updated: googleEvent.updated,
      etag: googleEvent.etag,
    })) || []

    // Return response with pagination info
    return {
      success: true,
      data: {
        events,
        nextPageToken: response.data.nextPageToken,
        nextSyncToken: response.data.nextSyncToken,
        summary: response.data.summary,
        description: response.data.description,
        timeZone: response.data.timeZone,
        accessRole: response.data.accessRole,
        defaultReminders: response.data.defaultReminders,
        updated: response.data.updated,
      },
      pagination: {
        nextPageToken: response.data.nextPageToken,
        nextSyncToken: response.data.nextSyncToken,
      },
    }
  }
  catch (error: any) {
    console.error('Google Calendar events list error:', error)

    // Handle specific Google API errors
    if (error.response?.status === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Google Calendar authorization expired. Please reconnect.',
      })
    }

    if (error.response?.status === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to access Google Calendar',
      })
    }

    if (error.response?.status === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Calendar not found',
      })
    }

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to fetch calendar events: ${error.message}`,
    })
  }
})
