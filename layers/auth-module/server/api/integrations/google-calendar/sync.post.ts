import { Timestamp } from 'firebase/firestore'
import { google } from 'googleapis'
import { useFirebaseServer } from '../../../firebase/init'
import { getUserSession } from '../../../utils/session'

// Convert Google Calendar event to our format
function googleEventToCalendarEvent(googleEvent: any, userId: string, workspaceId: string) {
  const startDate = new Date(googleEvent.start?.dateTime || googleEvent.start?.date)
  const endDate = new Date(googleEvent.end?.dateTime || googleEvent.end?.date)

  return {
    title: googleEvent.summary || 'Untitled Event',
    description: googleEvent.description || '',
    location: googleEvent.location || '',
    startDate: Timestamp.fromDate(startDate),
    endDate: Timestamp.fromDate(endDate),
    duration: Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60)), // duration in minutes
    category: 'personal',
    status: 'confirmed',
    userId,
    workspaceId,
    participants: googleEvent.attendees?.map((attendee: any) => ({
      id: Math.random(), // Generate random ID for now
      name: attendee.displayName || attendee.email,
      photo: '/img/avatars/default.svg',
    })) || [],
    googleEventId: googleEvent.id,
    syncedAt: Timestamp.now(),
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  }
}

// Convert our calendar event to Google format
function calendarEventToGoogle(event: any) {
  return {
    summary: event.title,
    description: event.description || '',
    location: event.location || '',
    start: {
      dateTime: event.startDate.toDate().toISOString(),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
    end: {
      dateTime: event.endDate.toDate().toISOString(),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
    attendees: event.participants?.map((participant: any) => ({
      email: participant.email || `${participant.name.toLowerCase().replace(/\s+/g, '.')}@example.com`,
      displayName: participant.name,
    })) || [],
  }
}

export default defineEventHandler(async (event) => {
  try {
    // Get session and Firebase instance using the proper server-side approach
    const session = await getUserSession(event)
    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    const body = await readBody(event)
    const { integrationId, direction, eventIds } = body

    if (!integrationId || !direction) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Integration ID and direction are required',
      })
    }

    if (!['import', 'export', 'bidirectional'].includes(direction)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid sync direction',
      })
    }

    // Get integration document using Firebase client SDK
    const { doc, getDoc } = await import('firebase/firestore')
    const integrationDoc = await getDoc(doc(firestore, 'calendar_integrations', integrationId))

    if (!integrationDoc.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Integration not found',
      })
    }

    const integration = integrationDoc.data()

    // Setup Google Calendar API
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
    )

    oauth2Client.setCredentials({
      access_token: integration.credentials.accessToken,
      refresh_token: integration.credentials.refreshToken,
    })

    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })

    let importedCount = 0
    let exportedCount = 0

    // Import from Google Calendar
    if (direction === 'import' || direction === 'bidirectional') {
      const now = new Date()
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)

      const eventsResponse = await calendar.events.list({
        calendarId: integration.calendarId,
        timeMin: oneMonthAgo.toISOString(),
        timeMax: oneMonthFromNow.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
      })

      const googleEvents = eventsResponse.data.items || []

      for (const googleEvent of googleEvents) {
        if (!googleEvent.id)
          continue

        // Check if event already exists using Firebase client SDK
        const { collection, query: buildQuery, where, getDocs } = await import('firebase/firestore')

        const existingEventQuery = buildQuery(
          collection(firestore, 'calendar_events'),
          where('googleEventId', '==', googleEvent.id),
          where('workspaceId', '==', integration.workspaceId)
        )

        const existingEvent = await getDocs(existingEventQuery)

        if (existingEvent.empty) {
          // Create new event
          const calendarEvent = googleEventToCalendarEvent(
            googleEvent,
            integration.userId,
            integration.workspaceId,
          )

          const { addDoc } = await import('firebase/firestore')
          await addDoc(collection(firestore, 'calendar_events'), calendarEvent)
          importedCount++
        }
      }
    }

    // Export to Google Calendar
    if (direction === 'export' || direction === 'bidirectional') {
      const exportQuery = buildQuery(
        collection(firestore, 'calendar_events'),
        where('workspaceId', '==', integration.workspaceId),
        where('googleEventId', '==', null) // Only events not yet synced to Google
      )

      if (eventIds && eventIds.length > 0) {
        // Export specific events
        for (const eventId of eventIds) {
          const eventDoc = await getDoc(doc(firestore, 'calendar_events', eventId))
          if (eventDoc.exists()) {
            const eventData = eventDoc.data()

            if (!eventData.googleEventId) {
              const googleEvent = calendarEventToGoogle(eventData)

              const createdEvent = await calendar.events.insert({
                calendarId: integration.calendarId,
                requestBody: googleEvent,
              })

              // Update local event with Google ID using Firebase client SDK
              const { updateDoc } = await import('firebase/firestore')
              await updateDoc(doc(firestore, 'calendar_events', eventId), {
                googleEventId: createdEvent.data.id,
                syncedAt: Timestamp.now(),
                updatedAt: Timestamp.now(),
              })

              exportedCount++
            }
          }
        }
      }
      else {
        // Export all unsynced events
        const unsyncedEvents = await getDocs(exportQuery)

        for (const eventDoc of unsyncedEvents.docs) {
          const eventData = eventDoc.data()
          const googleEvent = calendarEventToGoogle(eventData)

          const createdEvent = await calendar.events.insert({
            calendarId: integration.calendarId,
            requestBody: googleEvent,
          })

          // Update local event with Google ID using Firebase client SDK
          await updateDoc(eventDoc.ref, {
            googleEventId: createdEvent.data.id,
            syncedAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
          })

          exportedCount++
        }
      }
    }

    // Update sync status using Firebase client SDK
    await updateDoc(doc(firestore, 'calendar_integrations', integrationId), {
      'syncStatus.lastSync': Timestamp.now(),
      'syncStatus.eventCount': importedCount + exportedCount,
      'updatedAt': Timestamp.now(),
    })

    return {
      success: true,
      importedCount,
      exportedCount,
      eventCount: importedCount + exportedCount,
      message: `Sync completed: ${importedCount} imported, ${exportedCount} exported`,
    }
  }
  catch (error) {
    console.error('Google Calendar sync error:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to sync Google Calendar: ${error.message}`,
    })
  }
})
