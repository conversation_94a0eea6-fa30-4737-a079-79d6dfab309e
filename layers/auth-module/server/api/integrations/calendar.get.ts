import { useFirebaseServer } from '../../firebase/init'
import { getUserSession } from '../../utils/session'

export default defineEventHandler(async (event) => {
  try {
    // Get session and Firebase instance using the proper server-side approach
    const session = await getUserSession(event)
    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    const queryParams = getQuery(event)
    const { workspaceId, userId } = queryParams

    if (!workspaceId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Workspace ID is required',
      })
    }

    // Get Firebase instance with session context
    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    // Build query using Firebase client SDK
    const { collection, query: buildQuery, where, getDocs } = await import('firebase/firestore')

    // Build the query with all conditions
    const queryConstraints = [where('workspaceId', '==', workspaceId)]
    if (userId) {
      queryConstraints.push(where('userId', '==', userId))
    }

    const firestoreQuery = buildQuery(
      collection(firestore, 'calendar_integrations'),
      ...queryConstraints
    )

    let snapshot
    try {
      snapshot = await getDocs(firestoreQuery)
    }
    catch (firestoreError) {
      console.error('Firestore query failed:', firestoreError)
      // Return empty list if Firestore query fails (e.g., emulator issues)
      return {
        success: true,
        integrations: [],
      }
    }

    const integrations = snapshot.docs.map((doc) => {
      const data = doc.data()
      return {
        id: doc.id,
        ...data,
        // Don't expose sensitive credentials
        credentials: {
          hasToken: !!data.credentials?.accessToken,
          expiryDate: data.credentials?.expiryDate,
        },
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        syncStatus: {
          ...data.syncStatus,
          lastSync: data.syncStatus?.lastSync?.toDate(),
          nextSync: data.syncStatus?.nextSync?.toDate(),
        },
      }
    })

    return {
      success: true,
      integrations,
    }
  }
  catch (error) {
    console.error('Calendar integrations fetch error:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to fetch calendar integrations: ${error.message}`,
    })
  }
})
