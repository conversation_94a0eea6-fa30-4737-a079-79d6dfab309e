<script setup lang="ts">
import { getEmailProvider } from '../../config/email-providers'

interface Props {
  providerName: string
  visible: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'success', integration: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Get provider config
const provider = computed(() => {
  if (!props.providerName)
    return null
  const providerId = props.providerName.toLowerCase().replace(/\s+/g, '').replace('mail', '').replace('server', '')
  return getEmailProvider(providerId) || getEmailProvider('custom')
})

// Form data
const formData = ref({
  name: '',
  email: '',
  password: '',
  appPassword: '',
  host: '',
  port: 993,
  secure: true,
  imapHost: '',
  imapPort: 993,
  smtpHost: '',
  smtpPort: 587,
  syncFrequency: 15,
  syncFolders: ['INBOX'],
  enableAutoSync: true,
  markAsRead: false,
  enableFilters: true,
  maxEmailsPerSync: 100,
  enableAttachments: true,
  attachmentSizeLimit: 25,
})

// Form validation
const errors = ref<Record<string, string>>({})
const isSubmitting = ref(false)
const toaster = useToast()

// Initialize form with provider defaults
watch(() => props.visible, (visible) => {
  if (visible && provider.value) {
    formData.value.name = `${props.providerName} Account`

    // Set default server config if available
    if (provider.value.serverConfig) {
      formData.value.imapHost = provider.value.serverConfig.imapHost
      formData.value.imapPort = provider.value.serverConfig.imapPort
      formData.value.smtpHost = provider.value.serverConfig.smtpHost
      formData.value.smtpPort = provider.value.serverConfig.smtpPort
      formData.value.secure = provider.value.serverConfig.secure
    }

    // Set default sync folders
    if (provider.value.supportedFolders.length > 0) {
      formData.value.syncFolders = [provider.value.supportedFolders[0]]
    }
  }
})

function validateForm() {
  errors.value = {}

  if (!formData.value.name.trim()) {
    errors.value.name = 'Account name is required'
  }

  if (!formData.value.email.trim()) {
    errors.value.email = 'Email address is required'
  }
  else if (!/^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/.test(formData.value.email)) {
    errors.value.email = 'Valid email address is required'
  }

  if (provider.value?.authType === 'password' || provider.value?.authType === 'app-password') {
    const passwordField = provider.value.authType === 'app-password' ? 'appPassword' : 'password'
    if (!formData.value[passwordField]?.trim()) {
      errors.value[passwordField] = `${provider.value.authType === 'app-password' ? 'App password' : 'Password'} is required`
    }
  }

  if (provider.value?.id === 'custom' || provider.value?.id === 'imap') {
    if (!formData.value.imapHost.trim()) {
      errors.value.imapHost = 'IMAP host is required'
    }
    if (!formData.value.smtpHost.trim()) {
      errors.value.smtpHost = 'SMTP host is required'
    }
  }

  return Object.keys(errors.value).length === 0
}

async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true

  try {
    // Create integration data structure
    const integrationData = {
      provider: provider.value?.id || 'custom',
      providerName: provider.value?.name || props.providerName,
      email: formData.value.email,
      name: formData.value.name,
      authType: provider.value?.authType || 'password',
      config: {
        ...formData.value,
        // Only include auth fields for the specific auth type
        password: provider.value?.authType === 'password' ? formData.value.password : undefined,
        appPassword: provider.value?.authType === 'app-password' ? formData.value.appPassword : undefined,
      },
    }

    // Save integration to database
    const savedIntegration = await $fetch('/api/integrations/email/create', {
      method: 'POST',
      body: integrationData,
    })

    toaster.add({
      title: 'Success',
      description: `${provider.value?.name} account connected successfully`,
      color: 'green',
    })

    // Emit the saved integration data to parent
    emit('success', savedIntegration.integration)
    emit('close')
  }
  catch (error) {
    toaster.add({
      title: 'Error',
      description: 'Failed to connect email account. Please check your credentials.',
      color: 'danger',
    })
  }
  finally {
    isSubmitting.value = false
  }
}

function handleClose() {
  formData.value = {
    name: '',
    email: '',
    password: '',
    appPassword: '',
    host: '',
    port: 993,
    secure: true,
    imapHost: '',
    imapPort: 993,
    smtpHost: '',
    smtpPort: 587,
    syncFrequency: 15,
    syncFolders: ['INBOX'],
    enableAutoSync: true,
    markAsRead: false,
    enableFilters: true,
    maxEmailsPerSync: 100,
    enableAttachments: true,
    attachmentSizeLimit: 25,
  }
  errors.value = {}
  emit('close')
}

function handleOAuthConnect() {
  // TODO: Implement OAuth flow
  toaster.add({
    title: 'OAuth',
    description: 'OAuth integration will be implemented in the next phase',
    color: 'blue',
  })
}
</script>

<template>
  <TairoModal
    :open="visible"
    size="lg"
    @close="handleClose"
  >
    <template #header>
      <div class="flex items-center gap-2">
        <Icon :name="provider?.icon || 'lucide:mail'" class="h-6 w-6" />
        <h3 class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white">
          Connect {{ providerName }}
        </h3>
      </div>
    </template>

    <div class="mx-auto w-full max-w-md p-4 md:p-6">
      <div class="space-y-4">
        <!-- Provider info -->
        <div class="rounded-lg bg-muted-100 p-4 dark:bg-muted-800/50">
          <p class="text-sm text-muted-600 dark:text-muted-400">
            {{ provider?.description }}
          </p>
          <div v-if="provider?.features" class="mt-3">
            <p class="text-xs font-medium text-muted-700 dark:text-muted-300 mb-2">
              Features:
            </p>
            <div class="flex flex-wrap gap-1">
              <BaseTag
                v-for="feature in provider.features.slice(0, 3)"
                :key="feature"
                size="sm"
                rounded="full"
                variant="muted"
              >
                {{ feature }}
              </BaseTag>
            </div>
          </div>
        </div>

        <!-- OAuth providers -->
        <div v-if="provider?.authType === 'oauth'" class="text-center">
          <BaseButton
            size="lg"
            rounded="lg"
            class="w-full"
            @click="handleOAuthConnect"
          >
            <Icon :name="provider.icon" class="h-5 w-5 mr-2" />
            Connect with {{ providerName }}
          </BaseButton>
          <p class="mt-2 text-xs text-muted-500 dark:text-muted-400">
            You'll be redirected to {{ providerName }} to authorize access
          </p>
        </div>

        <!-- Password/App Password providers -->
        <form v-else class="space-y-4" @submit.prevent="handleSubmit">
          <!-- Account name -->
          <BaseInput
            v-model="formData.name"
            label="Account Name"
            placeholder="My Email Account"
            :error="errors.name"
            required
          />

          <!-- Email address -->
          <BaseInput
            v-model="formData.email"
            type="email"
            label="Email Address"
            placeholder="<EMAIL>"
            :error="errors.email"
            required
          />

          <!-- Password or App Password -->
          <BaseInput
            v-if="provider?.authType === 'password'"
            v-model="formData.password"
            type="password"
            label="Password"
            placeholder="Enter your password"
            :error="errors.password"
            required
          />

          <BaseInput
            v-if="provider?.authType === 'app-password'"
            v-model="formData.appPassword"
            type="password"
            label="App Password"
            placeholder="Enter your app-specific password"
            :error="errors.appPassword"
            required
          />

          <!-- Custom server settings -->
          <div v-if="provider?.id === 'custom' || provider?.id === 'imap'" class="space-y-4">
            <h4 class="text-sm font-medium text-muted-900 dark:text-white">
              Server Settings
            </h4>

            <div class="grid grid-cols-2 gap-4">
              <BaseInput
                v-model="formData.imapHost"
                label="IMAP Host"
                placeholder="imap.example.com"
                :error="errors.imapHost"
                required
              />
              <BaseInput
                v-model.number="formData.imapPort"
                type="number"
                label="IMAP Port"
                placeholder="993"
              />
            </div>

            <div class="grid grid-cols-2 gap-4">
              <BaseInput
                v-model="formData.smtpHost"
                label="SMTP Host"
                placeholder="smtp.example.com"
                :error="errors.smtpHost"
                required
              />
              <BaseInput
                v-model.number="formData.smtpPort"
                type="number"
                label="SMTP Port"
                placeholder="587"
              />
            </div>

            <BaseCheckbox
              v-model="formData.secure"
              label="Use SSL/TLS encryption"
            />
          </div>

          <!-- Sync settings -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium text-muted-900 dark:text-white">
              Sync Settings
            </h4>

            <div class="grid grid-cols-2 gap-4">
              <BaseInput
                v-model.number="formData.syncFrequency"
                type="number"
                label="Sync Frequency (minutes)"
                placeholder="15"
                min="1"
                max="1440"
              />
              <BaseInput
                v-model.number="formData.maxEmailsPerSync"
                type="number"
                label="Max Emails per Sync"
                placeholder="100"
                min="10"
                max="1000"
              />
            </div>

            <BaseCheckbox
              v-model="formData.enableAutoSync"
              label="Enable automatic synchronization"
            />

            <BaseCheckbox
              v-model="formData.enableAttachments"
              label="Download email attachments"
            />
          </div>

          <!-- Submit buttons -->
          <div class="flex gap-3 pt-4">
            <BaseButton
              type="button"
              variant="muted"
              class="flex-1"
              @click="handleClose"
            >
              Cancel
            </BaseButton>
            <BaseButton
              type="submit"
              variant="primary"
              class="flex-1"
              :loading="isSubmitting"
            >
              Connect Account
            </BaseButton>
          </div>
        </form>
      </div>
    </div>
  </TairoModal>
</template>
