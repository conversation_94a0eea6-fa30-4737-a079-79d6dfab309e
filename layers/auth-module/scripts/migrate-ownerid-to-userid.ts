#!/usr/bin/env node
/**
 * Migration script to rename ownerId to userId in existing Firestore documents
 *
 * Usage:
 * 1. Install dependencies: npm install firebase-admin
 * 2. Set up service account credentials
 * 3. Run: npx tsx migrate-ownerid-to-userid.ts
 */

import { cert, initializeApp } from 'firebase-admin/app'
import { FieldValue, getFirestore } from 'firebase-admin/firestore'

// Initialize Firebase Admin SDK for emulator
// Check if we're using emulator (development) or production
const useEmulator = process.env.NODE_ENV !== 'production' && process.env.NUXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true'

if (useEmulator) {
  console.log('🔧 Using Firebase Emulator for migration...')
  // Set emulator environment variables
  process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080'
  process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099'

  // Initialize without credentials for emulator
  initializeApp({
    projectId: 'demo-project',
  })
}
else {
  console.log('🚀 Using Production Firebase...')
  // You'll need to provide your service account credentials
  const serviceAccount = JSON.parse(
    process.env.FIREBASE_SERVICE_ACCOUNT
    || '{"error": "Please set FIREBASE_SERVICE_ACCOUNT environment variable"}',
  )

  if (serviceAccount.error) {
    console.error('Error:', serviceAccount.error)
    process.exit(1)
  }

  initializeApp({
    credential: cert(serviceAccount),
  })
}

const db = getFirestore()

/**
 * Migration statistics
 */
const stats = {
  workspaces: {
    total: 0,
    migrated: 0,
    errors: 0,
  },
}

/**
 * Migrate workspaces collection
 */
async function migrateWorkspaces() {
  console.log('Starting workspace migration...')

  try {
    const workspacesSnapshot = await db.collection('workspaces').get()
    stats.workspaces.total = workspacesSnapshot.size

    console.log(`Found ${stats.workspaces.total} workspaces to migrate`)

    // Process in batches of 500 (Firestore batch limit)
    const batchSize = 500
    const batches: FirebaseFirestore.WriteBatch[] = []
    let currentBatch = db.batch()
    let operationCount = 0

    for (const doc of workspacesSnapshot.docs) {
      const data = doc.data()

      // Check if document has ownerId field
      if ('ownerId' in data && !('userId' in data)) {
        const updates: any = {
          userId: data.ownerId,
          updatedAt: new Date().toISOString(),
        }

        // Remove ownerId field by setting it to FieldValue.delete()
        updates.ownerId = FieldValue.delete()

        currentBatch.update(doc.ref, updates)
        operationCount++
        stats.workspaces.migrated++

        // Create new batch if current is full
        if (operationCount >= batchSize) {
          batches.push(currentBatch)
          currentBatch = db.batch()
          operationCount = 0
        }
      }
      else if ('userId' in data) {
        console.log(`Workspace ${doc.id} already migrated`)
      }
    }

    // Add remaining operations to batch
    if (operationCount > 0) {
      batches.push(currentBatch)
    }

    // Execute all batches
    console.log(`Executing ${batches.length} batches...`)
    for (let i = 0; i < batches.length; i++) {
      try {
        await batches[i].commit()
        console.log(`Batch ${i + 1}/${batches.length} committed successfully`)
      }
      catch (error) {
        console.error(`Error committing batch ${i + 1}:`, error)
        stats.workspaces.errors++
      }
    }
  }
  catch (error) {
    console.error('Error migrating workspaces:', error)
    stats.workspaces.errors++
  }
}

/**
 * Main migration function
 */
async function migrate() {
  console.log('='.repeat(50))
  console.log('Starting ownerId to userId migration')
  console.log('='.repeat(50))

  const startTime = Date.now()

  try {
    // Run migrations
    await migrateWorkspaces()

    // Print summary
    const duration = (Date.now() - startTime) / 1000
    console.log(`\n${'='.repeat(50)}`)
    console.log('Migration Summary')
    console.log('='.repeat(50))
    console.log(`Total duration: ${duration.toFixed(2)} seconds`)
    console.log('\nWorkspaces:')
    console.log(`  Total: ${stats.workspaces.total}`)
    console.log(`  Migrated: ${stats.workspaces.migrated}`)
    console.log(`  Errors: ${stats.workspaces.errors}`)

    if (stats.workspaces.errors > 0) {
      console.error('\n⚠️  Migration completed with errors!')
      process.exit(1)
    }
    else {
      console.log('\n✅ Migration completed successfully!')
    }
  }
  catch (error) {
    console.error('Fatal error during migration:', error)
    process.exit(1)
  }
}

// Run migration
migrate().catch(console.error)
