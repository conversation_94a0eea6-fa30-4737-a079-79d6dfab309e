{"name": "@pib/ai-module", "type": "module", "version": "1.0.0", "private": true, "scripts": {"prepare": "nuxt prepare", "dev": "nuxt dev --open", "build": "nuxt build", "generate": "nuxt generate", "typecheck": "nuxt typecheck", "clean": "rimraf .nuxt .output node_modules", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:unit": "vitest run --testNamePattern=\"^(?!.*\\.(integration|e2e)).*\"", "test:integration": "vitest run --testNamePattern=\"\\.integration\"", "test:e2e": "playwright test", "test:watch": "vitest --watch", "redis:start": "docker-compose -f ../../docker-compose.redis.yml up -d", "redis:stop": "docker-compose -f ../../docker-compose.redis.yml down", "redis:logs": "docker-compose -f ../../docker-compose.redis.yml logs -f redis", "redis:cli": "docker exec -it pib-redis redis-cli", "ws:health": "curl -s http://localhost:3000/api/ai/health | jq", "ws:monitoring": "curl -s 'http://localhost:3000/api/ai/monitoring?performance=true&sessions=true&recovery=true' | jq", "ws:switch-scalable": "cd server/api/ai && mv ws.ts ws-original.ts && mv ws-scalable.ts ws.ts", "ws:switch-original": "cd server/api/ai && mv ws.ts ws-scalable.ts && mv ws-original.ts ws.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.0.10", "@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/vue": "^1.2.12", "@ai-sdk/xai": "^1.2.16", "@types/marked": "^6.0.0", "@vueuse/nuxt": "^13.4.0", "ai": "^4.3.16", "express-rate-limit": "^7.1.5", "ioredis": "^5.3.2", "isomorphic-dompurify": "^2.16.0", "marked": "^16.0.0", "ollama-ai-provider": "^0.16.1", "rate-limiter-flexible": "^4.0.1", "redis": "^4.6.12", "validator": "^13.11.0", "zod": "^3.25.67"}, "devDependencies": {"@iconify-json/fa6-brands": "^1.2.5", "@iconify-json/ph": "^1.2.2", "@iconify-json/simple-icons": "^1.2.30", "@nuxt/fonts": "^0.11.1", "@nuxt/test-utils": "^3.15.0", "@playwright/test": "^1.48.0", "@shuriken-ui/nuxt": "4.0.0-beta.4", "@testing-library/vue": "^8.1.0", "@types/dompurify": "^3.1.0", "@types/node": "^20.0.0", "@vitejs/plugin-vue": "^5.2.4", "@vitest/coverage-v8": "^2.1.0", "@vitest/ui": "^2.1.0", "@vue/test-utils": "^2.4.6", "happy-dom": "^15.0.0", "msw": "^2.0.0", "nuxt": "3.16.2", "tailwindcss": "^4.1.3", "typescript": "5.8.3", "vitest": "^2.1.0"}}