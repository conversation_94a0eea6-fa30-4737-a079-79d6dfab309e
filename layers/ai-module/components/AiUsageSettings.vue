<!--
/**
 * AiUsageSettings - AI Usage Limits Configuration Component
 *
 * A comprehensive Vue 3 component for configuring and managing AI usage limits,
 * cost controls, and rate limiting settings at both workspace and per-user levels.
 * Designed for workspace administrators to set granular usage controls.
 *
 * Features:
 * - Workspace-level usage limits (tokens, cost, requests)
 * - Per-user granular limits with toggle control
 * - Daily and monthly limit configurations
 * - Real-time cost conversion (cents ↔ dollars)
 * - Form validation with error handling
 * - Auto-loading of current settings
 * - Reset to default values functionality
 * - Optimistic UI updates with loading states
 * - Success/error message feedback system
 *
 * Architecture:
 * - Built with Vue 3 Composition API for reactive state
 * - Uses TypeScript for strong typing with UsageLimits interface
 * - Implements computed properties for currency conversion
 * - Integrates with backend API for persistence
 * - Form-based configuration with validation
 *
 * Performance Considerations:
 * - Lazy loading of settings on component mount
 * - Optimistic UI updates during save operations
 * - Efficient two-way data binding with v-model
 * - Auto-hide success messages to reduce UI clutter
 *
 * Security Features:
 * - Workspace-scoped limit configuration
 * - Input validation and sanitization
 * - Error boundary handling for API failures
 * - Secure form submission with POST requests
 *
 * Data Flow:
 * 1. Loads current settings from `/api/ai/usage/limits` on mount
 * 2. Manages form state with reactive refs and computed properties
 * 3. Validates and saves changes via POST to `/api/ai/usage/limits`
 * 4. Provides user feedback through success/error messaging
 *
 * @component AiUsageSettings
 * @example
 * <AiUsageSettings :workspaceId="currentWorkspace.id" />
 */
-->
<script setup lang="ts">
import type { UsageLimits } from '../types/rate-limiting'

/**
 * Component Props Interface
 *
 * @interface Props
 * @property {string} workspaceId - The workspace ID to configure usage limits for
 */
interface Props {
  workspaceId: string
}

const props = defineProps<Props>()

// ===== REACTIVE STATE =====
/**
 * Loading state for initial settings fetch
 * Controls loading spinner display while fetching current settings
 * @type {Ref<boolean>}
 */
const loading = ref(true)

/**
 * Saving state for form submission
 * Controls loading state of save button during API requests
 * @type {Ref<boolean>}
 */
const saving = ref(false)

/**
 * Toggle for per-user limits feature
 * Controls whether per-user limits section is enabled and included in saves
 * @type {Ref<boolean>}
 */
const enablePerUserLimits = ref(false)

/**
 * User feedback message state
 * Displays success or error messages to the user after operations
 * @type {Ref<{type: 'success' | 'error', text: string} | null>}
 */
const message = ref<{ type: 'success' | 'error', text: string } | null>(null)

// ===== FORM DATA =====
/**
 * Usage limits form data with default values
 *
 * Manages all usage limit configurations including workspace-level
 * and per-user limits. Default values provide sensible starting points
 * for new workspace configurations.
 *
 * Default Values:
 * - Daily Token Limit: 100,000 tokens
 * - Monthly Token Limit: 1,000,000 tokens
 * - Daily Cost Limit: $10.00 (1000 cents)
 * - Monthly Cost Limit: $100.00 (10000 cents)
 * - Daily Request Limit: 500 requests
 * - Monthly Request Limit: 10,000 requests
 * - Per-user limits: 20% of workspace limits
 *
 * @type {Ref<Partial<UsageLimits>>}
 */
const limits = ref<Partial<UsageLimits>>({
  dailyTokenLimit: 100000,
  monthlyTokenLimit: 1000000,
  dailyCostLimit: 1000, // $10 in cents
  monthlyCostLimit: 10000, // $100 in cents
  dailyRequestLimit: 500,
  monthlyRequestLimit: 10000,
  perUserLimits: {
    dailyTokenLimit: 20000,
    monthlyTokenLimit: 200000,
    dailyRequestLimit: 100,
    monthlyRequestLimit: 2000,
  },
})

// ===== COMPUTED PROPERTIES =====
/**
 * Two-way computed property for daily cost in dollars
 *
 * Converts between the internal cent-based storage format and the
 * user-friendly dollar display format. Handles currency conversion
 * with proper rounding to prevent floating-point precision issues.
 *
 * Conversion Logic:
 * - GET: cents / 100 = dollars (e.g., 1000 cents = $10.00)
 * - SET: dollars * 100 = cents (e.g., $12.34 = 1234 cents)
 *
 * Complexity: O(1)
 *
 * @returns {WritableComputedRef<number>} Dollar amount for daily cost limit
 *
 * @example
 * dailyCostDollars.value = 15.50 // Sets dailyCostLimit to 1550 cents
 * console.log(dailyCostDollars.value) // Returns 15.50 when dailyCostLimit is 1550
 */
const dailyCostDollars = computed({
  get: () => (limits.value.dailyCostLimit || 0) / 100,
  set: (value) => { limits.value.dailyCostLimit = Math.round(value * 100) },
})

/**
 * Two-way computed property for monthly cost in dollars
 *
 * Converts between the internal cent-based storage format and the
 * user-friendly dollar display format. Handles currency conversion
 * with proper rounding to prevent floating-point precision issues.
 *
 * Conversion Logic:
 * - GET: cents / 100 = dollars (e.g., 10000 cents = $100.00)
 * - SET: dollars * 100 = cents (e.g., $250.75 = 25075 cents)
 *
 * Complexity: O(1)
 *
 * @returns {WritableComputedRef<number>} Dollar amount for monthly cost limit
 *
 * @example
 * monthlyCostDollars.value = 250.75 // Sets monthlyCostLimit to 25075 cents
 * console.log(monthlyCostDollars.value) // Returns 250.75 when monthlyCostLimit is 25075
 */
const monthlyCostDollars = computed({
  get: () => (limits.value.monthlyCostLimit || 0) / 100,
  set: (value) => { limits.value.monthlyCostLimit = Math.round(value * 100) },
})

// ===== COMPONENT METHODS =====
/**
 * Load current usage settings from the API
 *
 * Fetches the existing usage limits configuration for the workspace
 * and populates the form with current values. Handles loading states
 * and error conditions gracefully.
 *
 * API Endpoint: GET `/api/ai/usage/limits?workspaceId=${workspaceId}`
 *
 * Side Effects:
 * - Sets loading state during fetch operation
 * - Updates limits reactive ref with fetched data
 * - Updates enablePerUserLimits based on configuration
 * - Clears any existing error messages
 * - Shows error message if fetch fails
 *
 * Error Handling:
 * - Catches and logs API errors
 * - Displays user-friendly error message
 * - Ensures loading state is properly cleared
 *
 * Complexity: O(1) for API call + O(1) for data assignment
 *
 * @async
 * @returns {Promise<void>} Resolves when settings are loaded or error is handled
 *
 * @example
 * await loadCurrentSettings() // Loads current workspace limits
 */
async function loadCurrentSettings() {
  try {
    loading.value = true
    const response = await $fetch(`/api/ai/usage/limits?workspaceId=${props.workspaceId}`)

    if (response.limits) {
      limits.value = { ...response.limits }
      enablePerUserLimits.value = !!response.limits.perUserLimits
    }

    message.value = null
  }
  catch (error) {
    console.error('Failed to load usage limits:', error)
    message.value = {
      type: 'error',
      text: 'Failed to load current settings',
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * Save usage settings to the API
 *
 * Submits the current form data to persist usage limit configurations.
 * Handles conditional inclusion of per-user limits based on toggle state.
 * Provides comprehensive error handling and user feedback.
 *
 * API Endpoint: POST `/api/ai/usage/limits`
 *
 * Payload Processing:
 * - Creates copy of limits to avoid mutations
 * - Conditionally removes perUserLimits if disabled
 * - Includes workspaceId for proper scoping
 *
 * User Experience:
 * - Shows saving state with button spinner
 * - Displays success message for 3 seconds
 * - Shows persistent error messages until cleared
 * - Clears existing messages before operation
 *
 * Error Handling:
 * - Catches and logs API errors
 * - Extracts user-friendly error messages from response
 * - Falls back to generic error message
 * - Ensures saving state is properly cleared
 *
 * Complexity: O(1) for API call + O(1) for payload processing
 *
 * @async
 * @returns {Promise<void>} Resolves when save is complete or error is handled
 *
 * @example
 * await saveSettings() // Saves current form state to API
 */
async function saveSettings() {
  try {
    saving.value = true
    message.value = null

    const payload = { ...limits.value }

    // Remove per-user limits if disabled
    if (!enablePerUserLimits.value) {
      delete payload.perUserLimits
    }

    const response = await $fetch('/api/ai/usage/limits', {
      method: 'POST',
      body: {
        workspaceId: props.workspaceId,
        limits: payload,
      },
    })

    message.value = {
      type: 'success',
      text: 'Usage limits updated successfully',
    }

    // Auto-hide success message after 3 seconds
    setTimeout(() => {
      message.value = null
    }, 3000)
  }
  catch (error: any) {
    console.error('Failed to save usage limits:', error)
    message.value = {
      type: 'error',
      text: error.data?.message || 'Failed to save settings',
    }
  }
  finally {
    saving.value = false
  }
}

/**
 * Reset form to default values
 *
 * Restores all usage limit configurations to their default values.
 * Provides a quick way for administrators to start with recommended
 * settings or undo problematic changes.
 *
 * Default Values Applied:
 * - Daily Token Limit: 100,000 tokens
 * - Monthly Token Limit: 1,000,000 tokens
 * - Daily Cost Limit: $10.00 (1000 cents)
 * - Monthly Cost Limit: $100.00 (10000 cents)
 * - Daily Request Limit: 500 requests
 * - Monthly Request Limit: 10,000 requests
 * - Per-user limits: Enabled with 20% of workspace limits
 *
 * Side Effects:
 * - Replaces entire limits object with defaults
 * - Enables per-user limits feature
 * - Clears any existing error/success messages
 * - Does not automatically save (requires manual save)
 *
 * Complexity: O(1)
 *
 * @returns {void}
 *
 * @example
 * resetToDefaults() // Resets form to recommended default values
 */
function resetToDefaults() {
  limits.value = {
    dailyTokenLimit: 100000,
    monthlyTokenLimit: 1000000,
    dailyCostLimit: 1000, // $10 in cents
    monthlyCostLimit: 10000, // $100 in cents
    dailyRequestLimit: 500,
    monthlyRequestLimit: 10000,
    perUserLimits: {
      dailyTokenLimit: 20000,
      monthlyTokenLimit: 200000,
      dailyRequestLimit: 100,
      monthlyRequestLimit: 2000,
    },
  }
  enablePerUserLimits.value = true
  message.value = null
}

// ===== LIFECYCLE HOOKS =====
/**
 * Component mounted lifecycle hook
 *
 * Automatically loads current settings when the component is mounted.
 * Ensures the form is populated with existing configuration data
 * on initial render.
 *
 * @see loadCurrentSettings() for implementation details
 */
onMounted(() => {
  loadCurrentSettings()
})
</script>

<template>
  <div class="ai-usage-settings">
    <!-- Header -->
    <div class="mb-6">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">
        Usage Limits
      </h2>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        Configure rate limits and cost controls for your workspace
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
    </div>

    <!-- Form -->
    <form v-else class="space-y-8" @submit.prevent="saveSettings">
      <!-- Workspace Limits -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Workspace Limits
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Daily Limits -->
          <div class="space-y-4">
            <h4 class="text-md font-medium text-gray-700 dark:text-gray-300">
              Daily Limits
            </h4>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Token Limit
              </label>
              <input
                v-model.number="limits.dailyTokenLimit"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="100000"
              >
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Maximum tokens per day for the workspace
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Cost Limit (USD)
              </label>
              <input
                v-model.number="dailyCostDollars"
                type="number"
                min="0"
                step="0.01"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="10.00"
              >
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Maximum cost per day for the workspace
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Request Limit
              </label>
              <input
                v-model.number="limits.dailyRequestLimit"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="500"
              >
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Maximum requests per day for the workspace
              </p>
            </div>
          </div>

          <!-- Monthly Limits -->
          <div class="space-y-4">
            <h4 class="text-md font-medium text-gray-700 dark:text-gray-300">
              Monthly Limits
            </h4>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Token Limit
              </label>
              <input
                v-model.number="limits.monthlyTokenLimit"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="1000000"
              >
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Maximum tokens per month for the workspace
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Cost Limit (USD)
              </label>
              <input
                v-model.number="monthlyCostDollars"
                type="number"
                min="0"
                step="0.01"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="100.00"
              >
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Maximum cost per month for the workspace
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Request Limit
              </label>
              <input
                v-model.number="limits.monthlyRequestLimit"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="10000"
              >
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Maximum requests per month for the workspace
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Per-User Limits -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Per-User Limits
          </h3>
          <div class="flex items-center">
            <input
              id="enable-per-user"
              v-model="enablePerUserLimits"
              type="checkbox"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
            <label for="enable-per-user" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Enable per-user limits
            </label>
          </div>
        </div>

        <div v-if="enablePerUserLimits" class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Daily Per-User Limits -->
          <div class="space-y-4">
            <h4 class="text-md font-medium text-gray-700 dark:text-gray-300">
              Daily Per-User Limits
            </h4>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Token Limit
              </label>
              <input
                v-model.number="limits.perUserLimits.dailyTokenLimit"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="20000"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Request Limit
              </label>
              <input
                v-model.number="limits.perUserLimits.dailyRequestLimit"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="100"
              >
            </div>
          </div>

          <!-- Monthly Per-User Limits -->
          <div class="space-y-4">
            <h4 class="text-md font-medium text-gray-700 dark:text-gray-300">
              Monthly Per-User Limits
            </h4>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Token Limit
              </label>
              <input
                v-model.number="limits.perUserLimits.monthlyTokenLimit"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="200000"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Request Limit
              </label>
              <input
                v-model.number="limits.perUserLimits.monthlyRequestLimit"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="2000"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-between">
        <button
          type="button"
          class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          @click="resetToDefaults"
        >
          Reset to Defaults
        </button>

        <div class="flex space-x-3">
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            @click="loadCurrentSettings"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="saving"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="saving" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Saving...
            </span>
            <span v-else>Save Settings</span>
          </button>
        </div>
      </div>
    </form>

    <!-- Success/Error Messages -->
    <div
      v-if="message" class="mt-4 p-4 rounded-md" :class="[
        message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200',
      ]"
    >
      {{ message.text }}
    </div>
  </div>
</template>

<style scoped>
.ai-usage-settings {
  @apply max-w-4xl mx-auto;
}
</style>
