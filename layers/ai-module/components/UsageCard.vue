<!--
/**
 * UsageCard - Reusable Usage Statistics Display Component
 *
 * A versatile Vue 3 component for displaying usage statistics with visual indicators,
 * progress bars, and contextual status messages. Designed for consistent presentation
 * of AI usage metrics including tokens, costs, and request counts.
 *
 * Features:
 * - Flexible value formatting with custom formatters
 * - Color-coded visual indicators with theme support
 * - Progress bar with percentage-based color transitions
 * - Status messages with emoji indicators
 * - Responsive design with dark mode support
 * - Optional limit comparison and percentage display
 * - Customizable icons and color themes
 * - Accessibility-friendly design patterns
 *
 * Visual Elements:
 * - Icon with themed background color
 * - Large current value display
 * - Optional limit comparison text
 * - Progress bar with contextual colors
 * - Status messages with warning levels
 * - Percentage badge with conditional styling
 *
 * Color Themes:
 * - Blue: General usage metrics
 * - Green: Cost-related metrics
 * - Purple: Request-based metrics
 * - Red: Critical alerts and warnings
 * - Amber: Caution and moderate warnings
 *
 * Status Levels:
 * - Normal (0-49%): Green with checkmark
 * - Moderate (50-74%): Blue with chart emoji
 * - High (75-89%): Amber with lightning emoji
 * - Critical (90%+): Red with warning emoji
 *
 * @component UsageCard
 * @example
 * <UsageCard
 *   title="Token Usage"
 *   :current="85000"
 *   :limit="100000"
 *   :percentage="85"
 *   unit="tokens"
 *   icon="ph:hash"
 *   color="blue"
 *   period="Daily"
 * />
 */
-->
<script setup lang="ts">
/**
 * Component Props Interface
 *
 * @interface Props
 * @property {string} title - Display title for the usage metric
 * @property {number} current - Current usage value
 * @property {number} [limit] - Optional usage limit for comparison
 * @property {number} [percentage] - Optional percentage of limit used
 * @property {string} unit - Unit of measurement (e.g., "tokens", "requests")
 * @property {string} icon - Icon name for the metric (Phosphor Icons format)
 * @property {'blue' | 'green' | 'purple' | 'red' | 'amber'} color - Theme color for the card
 * @property {Function} [formatValue] - Optional custom value formatter function
 * @property {string} [period] - Time period descriptor (defaults to "Current")
 */
interface Props {
  title: string
  current: number
  limit?: number
  percentage?: number
  unit: string
  icon: string
  color: 'blue' | 'green' | 'purple' | 'red' | 'amber'
  formatValue?: (value: number) => string
  period?: string
}

const props = withDefaults(defineProps<Props>(), {
  period: 'Current',
})

// ===== COLOR CONFIGURATION =====
/**
 * Theme color mappings for consistent visual design
 *
 * Maps theme colors to Tailwind CSS classes for both background
 * and text colors, with support for dark mode variants.
 *
 * Each color theme includes:
 * - bg: Background color for icon container
 * - text: Text/icon color matching the theme
 *
 * @type {Record<string, {bg: string, text: string}>}
 */
const colorMappings = {
  blue: {
    bg: 'bg-blue-100 dark:bg-blue-900',
    text: 'text-blue-600 dark:text-blue-400',
  },
  green: {
    bg: 'bg-green-100 dark:bg-green-900',
    text: 'text-green-600 dark:text-green-400',
  },
  purple: {
    bg: 'bg-purple-100 dark:bg-purple-900',
    text: 'text-purple-600 dark:text-purple-400',
  },
  red: {
    bg: 'bg-red-100 dark:bg-red-900',
    text: 'text-red-600 dark:text-red-400',
  },
  amber: {
    bg: 'bg-amber-100 dark:bg-amber-900',
    text: 'text-amber-600 dark:text-amber-400',
  },
}

/**
 * Computed color classes based on selected theme
 *
 * Returns the appropriate CSS classes for the current color theme.
 * Used to style the icon container and icon color consistently.
 *
 * @returns {object} Color classes object with bg and text properties
 */
const colorClasses = computed(() => colorMappings[props.color])

// ===== UTILITY FUNCTIONS =====
/**
 * Format large numbers with appropriate suffixes (K, M)
 *
 * Converts large numbers to more readable formats with suffixes.
 * Used as default formatter when no custom formatValue is provided.
 *
 * Format Rules:
 * - >= 1,000,000: Shows as millions with 1 decimal place (e.g., "1.5M")
 * - >= 1,000: Shows as thousands with 1 decimal place (e.g., "15.2K")
 * - < 1,000: Shows with locale-specific formatting (e.g., "1,234")
 *
 * Complexity: O(1)
 *
 * @param {number} value - The number to format
 * @returns {string} Formatted number string
 *
 * @example
 * formatNumber(1500000) // Returns "1.5M"
 * formatNumber(15200) // Returns "15.2K"
 * formatNumber(1234) // Returns "1,234"
 */
function formatNumber(value: number): string {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  }
  else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  return value.toLocaleString()
}

/**
 * Get CSS classes for percentage badge based on usage level
 *
 * Returns appropriate Tailwind CSS classes for styling the percentage
 * badge with colors that indicate usage severity levels.
 *
 * Color Mapping:
 * - 90%+: Red (critical)
 * - 75-89%: Amber (high usage)
 * - 50-74%: Blue (moderate usage)
 * - 0-49%: Green (normal usage)
 *
 * Complexity: O(1)
 *
 * @param {number} percentage - Usage percentage (0-100)
 * @returns {string} Tailwind CSS classes for the percentage badge
 *
 * @example
 * getPercentageColor(95) // Returns red classes for critical usage
 * getPercentageColor(60) // Returns blue classes for moderate usage
 */
function getPercentageColor(percentage: number): string {
  if (percentage >= 90) {
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  else if (percentage >= 75) {
    return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'
  }
  else if (percentage >= 50) {
    return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
  }
  else {
    return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
}

/**
 * Get CSS classes for progress bar based on usage level
 *
 * Returns appropriate Tailwind CSS background color classes for the
 * progress bar to visually indicate usage severity levels.
 *
 * Color Mapping:
 * - 90%+: Red (critical warning)
 * - 75-89%: Amber (caution)
 * - 50-74%: Blue (moderate)
 * - 0-49%: Green (normal)
 *
 * Complexity: O(1)
 *
 * @param {number} percentage - Usage percentage (0-100)
 * @returns {string} Tailwind CSS background color class
 *
 * @example
 * getProgressBarColor(85) // Returns "bg-amber-500" for high usage
 * getProgressBarColor(30) // Returns "bg-green-500" for normal usage
 */
function getProgressBarColor(percentage: number): string {
  if (percentage >= 90) {
    return 'bg-red-500'
  }
  else if (percentage >= 75) {
    return 'bg-amber-500'
  }
  else if (percentage >= 50) {
    return 'bg-blue-500'
  }
  else {
    return 'bg-green-500'
  }
}
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center">
        <div
          class="w-10 h-10 rounded-lg flex items-center justify-center mr-3" :class="[
            colorClasses.bg,
          ]"
        >
          <Icon :name="icon" class="h-5 w-5" :class="[colorClasses.text]" />
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-900 dark:text-white">
            {{ title }}
          </h3>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            {{ period }} usage
          </p>
        </div>
      </div>
      <div
        v-if="percentage !== undefined" class="px-2 py-1 rounded-full text-xs font-medium" :class="[
          getPercentageColor(percentage),
        ]"
      >
        {{ percentage.toFixed(1) }}%
      </div>
    </div>

    <!-- Usage Values -->
    <div class="mb-4">
      <div class="text-2xl font-semibold text-gray-900 dark:text-white">
        {{ formatValue ? formatValue(current) : formatNumber(current) }}
      </div>
      <div v-if="limit" class="text-sm text-gray-500 dark:text-gray-400">
        of {{ formatValue ? formatValue(limit) : formatNumber(limit) }} {{ unit }}
      </div>
      <div v-else class="text-sm text-gray-500 dark:text-gray-400">
        {{ unit }} used
      </div>
    </div>

    <!-- Progress Bar -->
    <div v-if="limit && percentage !== undefined" class="mb-2">
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          class="h-2 rounded-full transition-all duration-300" :class="[
            getProgressBarColor(percentage),
          ]"
          :style="{ width: `${Math.min(percentage, 100)}%` }"
        />
      </div>
    </div>

    <!-- Status Message -->
    <div v-if="percentage !== undefined" class="text-xs">
      <span v-if="percentage >= 90" class="text-red-600 dark:text-red-400 font-medium">
        ⚠️ Approaching limit
      </span>
      <span v-else-if="percentage >= 75" class="text-amber-600 dark:text-amber-400 font-medium">
        ⚡ High usage
      </span>
      <span v-else-if="percentage >= 50" class="text-blue-600 dark:text-blue-400">
        📊 Moderate usage
      </span>
      <span v-else class="text-green-600 dark:text-green-400">
        ✅ Normal usage
      </span>
    </div>
  </div>
</template>
