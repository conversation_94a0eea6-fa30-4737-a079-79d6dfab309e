<!--
/**
 * AiUsageDashboard - AI Usage Analytics and Monitoring Component
 *
 * A comprehensive Vue 3 component that provides real-time AI usage analytics,
 * cost monitoring, rate limit tracking, and detailed breakdowns by provider and user.
 * Designed for workspace administrators to monitor AI resource consumption and costs.
 *
 * Features:
 * - Real-time usage statistics with auto-refresh (30-second intervals)
 * - Multi-period analytics (daily/monthly views)
 * - Cost monitoring with formatted currency display
 * - Token usage tracking with intelligent number formatting
 * - Request count monitoring and rate limiting status
 * - Provider breakdown (OpenAI, Anthropic, Google, Ollama, etc.)
 * - User-specific usage analytics
 * - Warning system for usage limits and rate limits
 * - Responsive design with dark mode support
 *
 * Architecture:
 * - Built with Vue 3 Composition API for reactive state management
 * - Uses useLazyFetch for efficient data loading with caching
 * - Integrates with UsageCard component for consistent UI
 * - Implements automatic cleanup for performance optimization
 *
 * Performance Considerations:
 * - Lazy loading with default fallback data to prevent layout shifts
 * - Efficient number formatting functions to reduce computation
 * - Auto-refresh with cleanup to prevent memory leaks
 * - Computed properties for reactive configuration state
 *
 * Security Features:
 * - Workspace-scoped data access with proper validation
 * - Rate limiting status monitoring
 * - Cost monitoring with configurable limits
 * - User anonymization in display names
 *
 * Data Flow:
 * 1. Fetches usage statistics from `/api/ai/usage/stats`
 * 2. Processes raw data through formatting utilities
 * 3. Displays analytics in organized sections with visual indicators
 * 4. Auto-refreshes every 30 seconds for real-time monitoring
 *
 * @component AiUsageDashboard
 * @example
 * <AiUsageDashboard :workspaceId="currentWorkspace.id" />
 */
-->
<script setup lang="ts">
import { computed } from 'vue'

/**
 * Component Props Interface
 *
 * @interface Props
 * @property {string} workspaceId - The workspace ID to fetch usage analytics for
 */
interface Props {
  workspaceId: string
}

const props = defineProps<Props>()

// ===== REACTIVE STATE =====
/**
 * Selected time period for usage analytics
 * Controls the scope of data fetching and display
 * @type {Ref<'daily' | 'monthly'>}
 */
const selectedPeriod = ref<'daily' | 'monthly'>('daily')

/**
 * Available time period options for the selector
 * Provides user-friendly labels and consistent typing
 * @type {Array<{label: string, value: 'daily' | 'monthly'}>}
 */
const periodOptions = [
  { label: 'Daily', value: 'daily' as const },
  { label: 'Monthly', value: 'monthly' as const },
]

// ===== DATA FETCHING =====
/**
 * Fetch usage statistics from the API with reactive querying
 *
 * Uses useLazyFetch for efficient data loading with automatic caching
 * and reactivity. The query parameters update automatically when
 * workspaceId or selectedPeriod changes.
 *
 * Default Response Structure:
 * - stats: Core usage metrics (tokens, cost, requests)
 * - limits: Configured rate and cost limits
 * - usagePercentages: Calculated percentage usage
 * - warnings: Array of warning messages for limit violations
 *
 * Complexity: O(1) for API call + O(n) for data processing where n is number of users/providers
 *
 * @returns {object} Reactive fetch result with data, pending, error, and refresh
 */
const { data, pending, error, refresh } = await useLazyFetch(`/api/ai/usage/stats`, {
  query: computed(() => ({
    workspaceId: props.workspaceId,
    period: selectedPeriod.value,
  })),
  default: () => ({
    stats: {
      totalTokens: 0,
      totalCost: 0,
      totalRequests: 0,
      byProvider: {},
      byUser: {},
    },
    limits: null,
    usagePercentages: {},
    warnings: [],
  }),
})

// ===== CONFIGURATION =====
/**
 * Runtime configuration access for rate limiting settings
 */
const runtimeConfig = useRuntimeConfig()

/**
 * Computed property for rate limiting status
 * Determines if rate limiting is enabled in the current environment
 * @returns {boolean} True if rate limiting is enabled
 */
const rateLimitingEnabled = computed(() => runtimeConfig.public.rateLimiting?.enabled ?? true)

/**
 * Computed property for cost monitoring status
 * Determines if cost monitoring is enabled in the current environment
 * @returns {boolean} True if cost monitoring is enabled
 */
const costMonitoringEnabled = computed(() => runtimeConfig.public.rateLimiting?.costMonitoring ?? true)

// ===== UTILITY FUNCTIONS =====
/**
 * Format large numbers with appropriate suffixes (K, M)
 *
 * Converts large numbers to more readable formats with suffixes.
 * Uses locale-specific formatting for smaller numbers.
 *
 * Complexity: O(1)
 *
 * @param {number} value - The number to format
 * @returns {string} Formatted number string (e.g., "1.5M", "10.2K", "123")
 *
 * @example
 * formatNumber(1500000) // Returns "1.5M"
 * formatNumber(15000) // Returns "15.0K"
 * formatNumber(150) // Returns "150"
 */
function formatNumber(value: number): string {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  }
  else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  return value.toLocaleString()
}

/**
 * Format cost values from cents to dollar display
 *
 * Converts cost values stored in cents (integer) to formatted
 * dollar amounts with proper currency symbol and decimal places.
 *
 * Complexity: O(1)
 *
 * @param {number} value - Cost value in cents
 * @returns {string} Formatted currency string (e.g., "$12.34")
 *
 * @example
 * formatCost(1234) // Returns "$12.34"
 * formatCost(50) // Returns "$0.50"
 */
function formatCost(value: number): string {
  return `$${(value / 100).toFixed(2)}`
}

/**
 * Get color class for AI provider visualization
 *
 * Returns appropriate Tailwind CSS background color classes
 * for different AI providers to maintain consistent visual identity.
 *
 * Provider Color Mapping:
 * - OpenAI: Green (bg-green-500)
 * - Anthropic: Orange (bg-orange-500)
 * - Google: Blue (bg-blue-500)
 * - Ollama: Purple (bg-purple-500)
 * - Default: Gray (bg-gray-500)
 *
 * Complexity: O(1)
 *
 * @param {string} provider - Provider name (lowercase)
 * @returns {string} Tailwind CSS color class
 *
 * @example
 * getProviderColor('openai') // Returns "bg-green-500"
 * getProviderColor('unknown') // Returns "bg-gray-500"
 */
function getProviderColor(provider: string): string {
  const colors: Record<string, string> = {
    openai: 'bg-green-500',
    anthropic: 'bg-orange-500',
    google: 'bg-blue-500',
    ollama: 'bg-purple-500',
  }
  return colors[provider] || 'bg-gray-500'
}

/**
 * Get display name for user ID
 *
 * Converts user IDs to display-friendly names. Currently implements
 * simple truncation for privacy, but should be enhanced to fetch
 * actual user names from the authentication system.
 *
 * Security Note: Only shows first 8 characters of user ID for privacy
 *
 * Complexity: O(1)
 *
 * @param {string} userId - The user's unique identifier
 * @returns {string} User-friendly display name
 *
 * @example
 * getUserName('abc123def456') // Returns "User abc123de..."
 *
 * @todo Integrate with auth system to fetch real user names
 */
function getUserName(userId: string): string {
  // In a real app, you'd fetch user names from the auth system
  return `User ${userId.substring(0, 8)}...`
}

// ===== LIFECYCLE MANAGEMENT =====
/**
 * Auto-refresh interval for real-time data updates
 *
 * Automatically refreshes usage data every 30 seconds to provide
 * near real-time monitoring capabilities. The interval is cleared
 * on component unmount to prevent memory leaks.
 *
 * Refresh Frequency: 30 seconds (30000ms)
 */
const refreshInterval = setInterval(() => {
  refresh()
}, 30000)

/**
 * Component cleanup on unmount
 *
 * Ensures proper cleanup of the auto-refresh interval when the
 * component is unmounted to prevent memory leaks and unnecessary
 * API calls after the component is destroyed.
 */
onUnmounted(() => {
  clearInterval(refreshInterval)
})

// ===== REACTIVE EFFECTS =====
/**
 * Watch for period selection changes
 *
 * Automatically refreshes data when the user changes the time period
 * selection (daily/monthly) to immediately show updated analytics
 * for the new period scope.
 *
 * @param {string} selectedPeriod - The new selected period ('daily' | 'monthly')
 */
watch(selectedPeriod, () => {
  refresh()
})
</script>

<template>
  <div class="ai-usage-dashboard">
    <!-- Header -->
    <div class="mb-6">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">
        AI Usage Dashboard
      </h2>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        Monitor AI usage, costs, and rate limits for your workspace
      </p>
    </div>

    <!-- Period Selector -->
    <div class="mb-6">
      <div class="flex space-x-4">
        <button
          v-for="periodOption in periodOptions"
          :key="periodOption.value"
          class="px-4 py-2 rounded-lg text-sm font-medium transition-colors" :class="[
            selectedPeriod === periodOption.value
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700',
          ]"
          @click="selectedPeriod = periodOption.value"
        >
          {{ periodOption.label }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="pending" class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <Icon name="ph:warning-circle" class="h-5 w-5 text-red-400 mr-2" />
        <div>
          <h3 class="text-sm font-medium text-red-800">
            Error Loading Usage Data
          </h3>
          <p class="text-sm text-red-700 mt-1">
            {{ error.message }}
          </p>
        </div>
      </div>
    </div>

    <!-- Usage Statistics -->
    <div v-else-if="data" class="space-y-6">
      <!-- Warnings -->
      <div v-if="data.warnings?.length" class="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <div class="flex">
          <Icon name="ph:warning" class="h-5 w-5 text-amber-400 mr-2" />
          <div>
            <h3 class="text-sm font-medium text-amber-800">
              Usage Warnings
            </h3>
            <ul class="text-sm text-amber-700 mt-1 space-y-1">
              <li v-for="warning in data.warnings" :key="warning">
                {{ warning }}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Usage Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Tokens Usage -->
        <UsageCard
          title="Token Usage"
          :current="data.stats.totalTokens"
          :limit="data.limits?.[`${selectedPeriod}TokenLimit`]"
          :percentage="data.usagePercentages?.tokens"
          unit="tokens"
          icon="ph:hash"
          color="blue"
        />

        <!-- Cost Usage -->
        <UsageCard
          title="Cost Usage"
          :current="data.stats.totalCost"
          :limit="data.limits?.[`${selectedPeriod}CostLimit`]"
          :percentage="data.usagePercentages?.cost"
          unit="cents"
          :format-value="formatCost"
          icon="ph:currency-dollar"
          color="green"
        />

        <!-- Request Usage -->
        <UsageCard
          title="Request Usage"
          :current="data.stats.totalRequests"
          :limit="data.limits?.[`${selectedPeriod}RequestLimit`]"
          :percentage="data.usagePercentages?.requests"
          unit="requests"
          icon="ph:arrow-square-out"
          color="purple"
        />
      </div>

      <!-- Provider Breakdown -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Usage by Provider
        </h3>
        <div v-if="Object.keys(data.stats.byProvider).length > 0" class="space-y-4">
          <div
            v-for="(usage, provider) in data.stats.byProvider"
            :key="provider"
            class="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
          >
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full mr-3" :class="getProviderColor(provider)" />
              <span class="font-medium text-gray-900 dark:text-white capitalize">{{ provider }}</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900 dark:text-white">
                {{ formatNumber(usage.tokens) }} tokens
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatCost(usage.cost) }} • {{ usage.requests }} requests
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500 dark:text-gray-400">
          No usage data available for this period
        </div>
      </div>

      <!-- User Breakdown -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Usage by User
        </h3>
        <div v-if="Object.keys(data.stats.byUser).length > 0" class="space-y-4">
          <div
            v-for="(usage, userId) in data.stats.byUser"
            :key="userId"
            class="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
          >
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center mr-3">
                <Icon name="ph:user" class="h-4 w-4 text-gray-600 dark:text-gray-300" />
              </div>
              <span class="font-medium text-gray-900 dark:text-white">{{ getUserName(userId) }}</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900 dark:text-white">
                {{ formatNumber(usage.tokens) }} tokens
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatCost(usage.cost) }} • {{ usage.requests }} requests
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500 dark:text-gray-400">
          No user usage data available for this period
        </div>
      </div>

      <!-- Rate Limit Status -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Rate Limit Status
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Rate Limiting</span>
              <span
                class="px-2 py-1 rounded-full text-xs font-medium" :class="[
                  rateLimitingEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600',
                ]"
              >
                {{ rateLimitingEnabled ? 'Enabled' : 'Disabled' }}
              </span>
            </div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Cost Monitoring</span>
              <span
                class="px-2 py-1 rounded-full text-xs font-medium" :class="[
                  costMonitoringEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600',
                ]"
              >
                {{ costMonitoringEnabled ? 'Enabled' : 'Disabled' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.ai-usage-dashboard {
  @apply max-w-7xl mx-auto;
}
</style>
