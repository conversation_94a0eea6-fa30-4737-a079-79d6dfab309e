<script setup lang="ts">
import type { AiChatConfig } from '../types/ai'

interface Props {
  conversationId?: string | null
  collapsible?: boolean
  defaultCollapsed?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  toggleCollapse: [collapsed: boolean]
  expandArtifact: [artifactId: string]
}>()

// Get available models
const { availableModels, loading: modelsLoading } = useAiModels()

// Chat state
const selectedModelId = ref<string>('')
const inputMessage = ref('')
const chatInputRef = ref()
const isCollapsed = ref(props.defaultCollapsed || false)

// Get selected model details
const selectedModel = computed(() => {
  return availableModels.value.find(m => m.id === selectedModelId.value)
})

// Initialize chat instance once at the top level with default config
const defaultConfig: AiChatConfig = {
  integrationId: '',
  model: '',
  provider: '',
  temperature: 0.7,
  maxTokens: 4000,
}

const chatInstance = useAiChat(defaultConfig)

// Watch for model changes and clear messages when switching
watch(selectedModel, (model) => {
  if (model && selectedModelId.value) {
    // Clear previous messages when switching models
    chatInstance.clearMessages()

    // Set conversation ID if provided
    if (props.conversationId) {
      chatInstance.setConversationId(props.conversationId)
    }

    console.log('Switched to model:', model)
  }
})

// Send message
async function sendMessage() {
  if (!inputMessage.value.trim())
    return

  const message = inputMessage.value
  inputMessage.value = ''

  try {
    await chatInstance.sendChatMessage(message)
  }
  catch (error) {
    console.error('Error sending message:', error)
  }

  // Focus input after sending
  nextTick(() => {
    chatInputRef.value?.focusInput()
  })
}

// Computed properties from chat instance
const messages = computed(() => chatInstance.messages.value)
const isLoading = computed(() => chatInstance.isLoading.value)
const error = computed(() => chatInstance.error.value)
const artifacts = computed(() => chatInstance.artifacts.value)
const hasValidModel = computed(() => !!selectedModel.value && !!selectedModelId.value && chatInstance.isConfigValid.value)

// Scroll to bottom when new messages arrive
const messagesContainer = ref<HTMLElement>()
watch([messages], () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
})

// Handle collapse toggle
function toggleCollapse() {
  isCollapsed.value = !isCollapsed.value
  emit('toggleCollapse', isCollapsed.value)
}

// Handle artifact expansion from messages
function handleExpandArtifact(artifactId: string) {
  emit('expandArtifact', artifactId)
  // Auto-collapse chat if on mobile or if collapsible is enabled
  if (props.collapsible && window.innerWidth < 1024) {
    isCollapsed.value = true
    emit('toggleCollapse', true)
  }
}

// Clean up on unmount
onUnmounted(() => {
  // Chat instance cleanup is handled internally by useAiChat
})
</script>

<template>
  <div
    class="flex flex-col h-full transition-all duration-300"
    :class="{
      'w-16': isCollapsed,
      'w-full': !isCollapsed,
    }"
  >
    <!-- Header with model selector -->
    <div class="shrink-0 p-4 border-b border-muted-200 dark:border-muted-800">
      <div class="flex items-center gap-4">
        <!-- Collapse toggle button (when collapsible) -->
        <BaseButton
          v-if="collapsible"
          size="sm"
          variant="ghost"
          class="shrink-0"
          @click="toggleCollapse"
        >
          <Icon
            :name="isCollapsed ? 'lucide:panel-right-open' : 'lucide:panel-right-close'"
            class="size-4"
          />
        </BaseButton>
        <div v-if="!isCollapsed" class="flex-1 max-w-xs">
          <AiModelSelector
            v-model="selectedModelId"
            :models="availableModels"
            :loading="modelsLoading"
          />
        </div>

        <!-- Connection status -->
        <div v-if="!isCollapsed" class="flex items-center gap-2">
          <div
            class="size-2 rounded-full"
            :class="{
              'bg-success-500': hasValidModel,
              'bg-muted-300 dark:bg-muted-700': !hasValidModel,
            }"
          />
          <BaseText size="xs" class="text-muted-400">
            {{ hasValidModel ? 'Ready' : 'Select Model' }}
          </BaseText>
        </div>
      </div>

      <!-- Error message -->
      <BaseMessage v-if="error" type="danger" class="mt-2">
        {{ error }}
      </BaseMessage>
    </div>

    <!-- Messages container -->
    <div
      ref="messagesContainer"
      class="flex-1 overflow-y-auto px-4 py-6 nui-slimscroll"
    >
      <!-- Empty state -->
      <div
        v-if="messages.length === 0"
        class="flex flex-col items-center justify-center h-full text-center"
      >
        <div class="dark:bg-muted-950 border-muted-300 dark:border-muted-800 mx-auto mb-4 flex size-16 items-center justify-center rounded-xl border bg-white">
          <div class="bg-muted-100 dark:bg-muted-800 flex size-14 items-center justify-center rounded-lg">
            <TairoLogo class="text-primary-500 size-8" />
          </div>
        </div>
        <BaseHeading size="lg" weight="medium" class="mb-2">
          Start a conversation
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-400 max-w-sm">
          Select an AI model above and send a message to begin chatting.
        </BaseParagraph>
      </div>

      <!-- Messages -->
      <div v-else class="space-y-2">
        <AiChatMessage
          v-for="message in messages"
          :key="message.id"
          :message="message"
          @expand-artifact="handleExpandArtifact"
        />

        <!-- Loading indicator -->
        <div v-if="isLoading" class="flex justify-start">
          <div class="max-w-2xl bg-muted-100 dark:bg-muted-800 rounded-2xl px-4 py-3">
            <div class="flex items-center gap-2">
              <Icon name="svg-spinners:12-dots-scale-rotate" class="size-5 text-primary-500" />
              <span class="text-muted-600 dark:text-muted-300">AI is thinking...</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Input area -->
    <div class="shrink-0 p-4 border-t border-muted-200 dark:border-muted-800">
      <AiChatInput
        ref="chatInputRef"
        v-model="inputMessage"
        :disabled="!selectedModelId || isLoading"
        :placeholder="!selectedModelId ? 'Select a model to start chatting...' : 'Message AI assistant...'"
        @submit="sendMessage"
      />
    </div>
  </div>
</template>
