<!--
/**
 * AiChatInterface - Main AI Chat Interface Component
 *
 * A comprehensive Vue 3 component that provides a full-featured AI chat interface
 * with support for multiple AI models, conversation management, error handling,
 * and real-time messaging with streaming responses.
 *
 * Features:
 * - Multi-model AI support (OpenAI, Anthropic, Google, XAI, etc.)
 * - Real-time streaming chat responses
 * - Conversation persistence and loading
 * - Error handling with user-friendly notifications
 * - Responsive design with loading states
 * - Security validation and rate limiting
 * - Tool calling and artifact generation
 *
 * Architecture:
 * - Uses composition API for reactive state management
 * - Integrates with useAiChat composable for chat functionality
 * - Leverages useAiModels for model management
 * - Implements error boundaries and user feedback
 *
 * Performance Considerations:
 * - Lazy-loaded chat initialization to reduce initial bundle size
 * - Optimized re-renders with computed properties
 * - Efficient message list rendering with v-for keys
 *
 * @component AiChatInterface
 * @example
 * <AiChatInterface
 *   :selectedModel="'gemini-1.5-pro'"
 *   :conversationId="conversation?.id"
 * />
 */
-->
<script setup lang="ts">
import type { AiChatConfig } from '../types/ai'
import { computed, ref, watch, watchEffect } from 'vue'

/**
 * Component Props Interface
 *
 * @interface Props
 * @property {string | null} conversationId - Optional conversation ID to load existing chat
 * @property {string} selectedModel - The selected AI model ID for chat processing
 */
interface Props {
  conversationId?: string | null
  selectedModel?: string
}

const props = defineProps<Props>()

// ===== COMPOSABLE INITIALIZATION =====
/**
 * Initialize required composables for AI model management and user notifications
 * - useAiModels: Provides access to available AI models and model lookup functionality
 * - useToast: Handles user notification display for errors and success messages
 */
const { availableModels, getModelById } = useAiModels()
const toast = useToast()

// ===== REACTIVE STATE =====
/**
 * Input message reactive reference
 * Stores the current message being typed by the user
 * @type {Ref<string>}
 */
const inputMessage = ref('')

/**
 * Default AI Chat Configuration
 * Provides baseline configuration for the AI chat system
 * Used as initial state before model selection
 *
 * @type {AiChatConfig}
 * @property {string} integrationId - Empty until model is selected
 * @property {string} model - Empty until model is selected
 * @property {string} provider - Empty until model is selected
 * @property {number} temperature - Controls response creativity (0.7 = balanced)
 * @property {number} maxTokens - Maximum response length (4000 tokens)
 */
const defaultConfig: AiChatConfig = {
  integrationId: '',
  model: '',
  provider: '',
  temperature: 0.7, // Balanced creativity vs coherence
  maxTokens: 4000, // Generous token limit for comprehensive responses
}

/**
 * Initialize AI chat composable with default configuration
 * This creates the core chat functionality and maintains state
 * across component lifecycle and model switches
 */
const aiChat = useAiChat(defaultConfig)

// ===== COMPUTED PROPERTIES =====
/**
 * Generate chat configuration based on selected model
 *
 * This computed property creates a valid AiChatConfig when a model is selected,
 * or returns null if no model is selected or the model is invalid.
 *
 * Complexity: O(1) - Single model lookup and object creation
 *
 * Dependencies:
 * - props.selectedModel: The currently selected model ID
 * - getModelById: Model lookup function from useAiModels
 *
 * @returns {AiChatConfig | null} Valid chat config or null if invalid/no selection
 */
const chatConfig = computed<AiChatConfig | null>(() => {
  console.log('Creating chat config for selected model:', props.selectedModel)

  // Early return if no model is selected
  if (!props.selectedModel) {
    console.log('No selected model')
    return null
  }

  // Lookup the complete model information by ID
  const model = getModelById(props.selectedModel)
  console.log('Found model:', model)

  // Validate model exists and has required integration ID
  if (!model || !model.integrationId) {
    console.log('Invalid model or missing integrationId:', model)
    return null
  }

  // Create chat configuration with model-specific settings
  const config = {
    integrationId: model.integrationId!,
    model: model.model,
    provider: model.provider,
    temperature: 0.7, // Consistent temperature across all models
    maxTokens: 4000, // Consistent token limit
  }

  console.log('Created chat config:', config)
  return config
})

/**
 * Chat state computed properties
 * These provide reactive access to the current chat state from useAiChat composable
 */
const messages = computed(() => aiChat.messages.value) // Current conversation messages
const isLoading = computed(() => aiChat.isLoading.value) // Loading state for streaming responses
const error = computed(() => aiChat.error.value) // Any error from chat operations
const hasValidConfig = computed(() => aiChat.isConfigValid.value) // Whether config is valid for chat

// ===== REACTIVE EFFECTS =====
/**
 * Watch for configuration changes and update chat instance
 *
 * This effect automatically updates the chat configuration when the computed
 * chatConfig changes (e.g., when user selects a different model).
 * It also clears existing messages to start fresh with the new model.
 *
 * Side Effects:
 * - Updates aiChat configuration
 * - Clears message history for clean slate
 */
watchEffect(() => {
  if (chatConfig.value) {
    // Update the chat configuration with new model settings
    aiChat.updateConfig(chatConfig.value)
    // Clear messages when switching models for new conversation
    aiChat.clearMessages()
  }
})

/**
 * Watch for conversation ID changes and load conversation
 *
 * This watcher handles loading existing conversations when the conversationId prop
 * changes, or clearing messages when switching to a new conversation.
 *
 * Error Handling:
 * - Catches and logs conversation loading errors
 * - Shows user-friendly error toast notifications
 * - Gracefully handles network or permission errors
 *
 * @param {string | null} newId - The new conversation ID to load
 */
watch(() => props.conversationId, async (newId) => {
  if (newId && hasValidConfig.value) {
    try {
      // Attempt to load the specified conversation
      await aiChat.loadConversation(newId)
    }
    catch (error) {
      console.error('Error loading conversation:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to load conversation'
      toast.error(
        'Failed to Load Conversation',
        `Unable to load conversation: ${errorMessage}`,
      )
    }
  }
  else if (hasValidConfig.value) {
    // Clear messages if switching to new conversation or no conversation ID
    aiChat.clearMessages()
  }
})

// ===== COMPONENT METHODS =====
/**
 * Send a chat message to the AI
 *
 * This function handles the complete message sending flow including validation,
 * error handling, and user feedback. It implements optimistic UI updates by
 * clearing the input immediately, then restoring it if the send fails.
 *
 * Validation:
 * - Requires non-empty trimmed message
 * - Requires valid chat configuration
 *
 * Error Handling:
 * - Catches all sending errors
 * - Shows user-friendly error notifications
 * - Restores message to input for retry
 *
 * Complexity: O(1) for local operations + O(n) for network request where n is message length
 *
 * @async
 * @returns {Promise<void>} Resolves when message is sent or error is handled
 */
async function sendMessage() {
  // Validate preconditions for sending message
  if (!inputMessage.value.trim() || !hasValidConfig.value)
    return

  // Capture and clear the message for optimistic UI update
  const messageContent = inputMessage.value.trim()
  inputMessage.value = ''

  try {
    // Send the message through the chat composable
    await aiChat.sendChatMessage(messageContent)
  }
  catch (error) {
    console.error('Error sending message:', error)

    // Show user-friendly error toast with actionable message
    const errorMessage = error instanceof Error ? error.message : 'Failed to send message'
    toast.error(
      'Message Failed',
      `Unable to send your message: ${errorMessage}. Please try again.`,
    )

    // Restore the message to the input if sending failed (UX improvement)
    inputMessage.value = messageContent
  }
}

/**
 * Handle keyboard input for message sending
 *
 * Enables sending messages with Enter key while preserving Shift+Enter
 * for multi-line messages. This provides a natural chat interface UX.
 *
 * Key Combinations:
 * - Enter: Send message
 * - Shift+Enter: New line (default textarea behavior)
 *
 * @param {KeyboardEvent} event - The keyboard event from the textarea
 */
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault() // Prevent form submission or new line
    sendMessage()
  }
}
</script>

<!--
  TEMPLATE STRUCTURE:

  The template implements a responsive chat interface with multiple UI states:
  1. Error State: Displays when chat errors occur (network, auth, etc.)
  2. No Model State: Shown when no AI model is selected
  3. Empty State: Welcome screen with conversation starters
  4. Messages State: Active chat with message history and input

  Layout:
  - Full height flexbox container
  - Dynamic content area (flex-1)
  - Fixed input section at bottom

  Accessibility:
  - Proper ARIA roles and labels
  - Keyboard navigation support
  - Screen reader friendly content
  - Color contrast compliance
-->
<template>
  <div class="flex h-full flex-col">
    <!--
      ERROR STATE
      Displayed when there's a chat error (network issues, authentication failures, etc.)
      Provides user-friendly error message and retry option
    -->
    <div v-if="error" class="flex-1 flex items-center justify-center">
      <div class="text-center">
        <Icon name="lucide:alert-triangle" class="text-danger-500 size-12 mx-auto mb-4" />
        <h3 class="font-semibold text-lg mb-2">
          Chat Error
        </h3>
        <p class="text-muted-600 dark:text-muted-300 mb-4">
          {{ error }}
        </p>
        <BaseButton @click="aiChat.reload()">
          Try Again
        </BaseButton>
      </div>
    </div>

    <!--
      NO MODEL SELECTED STATE
      Shown when user hasn't selected an AI model yet
      Guides user to select a model from the dropdown
    -->
    <div v-else-if="!selectedModel" class="flex-1 flex items-center justify-center">
      <div class="text-center">
        <Icon name="lucide:bot" class="text-muted-400 size-12 mx-auto mb-4" />
        <h3 class="font-semibold text-lg mb-2">
          Select an AI Model
        </h3>
        <p class="text-muted-600 dark:text-muted-300">
          Choose an AI model from the dropdown above to start chatting.
        </p>
      </div>
    </div>

    <!--
      EMPTY STATE / WELCOME SCREEN
      Displayed when a model is selected but no messages exist yet
      Provides conversation starter examples and branding
    -->
    <div v-else-if="messages.length === 0" class="flex-1 flex items-center justify-center">
      <div>
        <!-- Logo section -->
        <div class="dark:bg-muted-950 border-muted-300 dark:border-muted-800 mx-auto mb-10 flex size-16 items-center justify-center rounded-xl border bg-white">
          <div class="bg-muted-100 dark:bg-muted-800 flex size-14 items-center justify-center rounded-lg">
            <TairoLogo class="text-primary-500 size-8" />
          </div>
        </div>
        <!-- Grid section -->
        <div class="grid grid-cols-2 gap-4 md:grid-cols-4">
          <div>
            <BaseCard
              rounded="lg"
              shadow="flat"
              class="hover:border-muted-900! dark:hover:border-muted-400! h-full p-4 cursor-pointer"
              role="button"
              @click="inputMessage = 'Create an image for my business presentation'"
            >
              <div class="mb-2">
                <Icon name="solar:archive-down-minimlistic-broken" class="text-primary-500 size-5" />
              </div>
              <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                Create an image for your business presentation
              </BaseParagraph>
            </BaseCard>
          </div>
          <div>
            <BaseCard
              rounded="lg"
              shadow="flat"
              class="hover:border-muted-900! dark:hover:border-muted-400! h-full p-4 cursor-pointer"
              role="button"
              @click="inputMessage = 'Make a summary of the last meeting'"
            >
              <div class="mb-2">
                <Icon name="solar:document-text-linear" class="text-success-500 size-5" />
              </div>
              <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                Make a summary of the last meeting
              </BaseParagraph>
            </BaseCard>
          </div>
          <div>
            <BaseCard
              rounded="lg"
              shadow="flat"
              class="hover:border-muted-900! dark:hover:border-muted-400! h-full p-4 cursor-pointer"
              role="button"
              @click="inputMessage = 'Retrieve the date and place of an uploaded image'"
            >
              <div class="mb-2">
                <Icon name="solar:camera-linear" class="text-destructive-500 size-5" />
              </div>
              <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                Retrieve the date and place of an uploaded image
              </BaseParagraph>
            </BaseCard>
          </div>
          <div>
            <BaseCard
              rounded="lg"
              shadow="flat"
              class="hover:border-muted-900! dark:hover:border-muted-400! h-full p-4 cursor-pointer"
              role="button"
              @click="inputMessage = 'Create a scenario for marketing leads and phone calls'"
            >
              <div class="mb-2">
                <Icon name="solar:call-chat-rounded-linear" class="size-5 text-yellow-400" />
              </div>
              <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                Create a scenario for marketing leads and phone calls
              </BaseParagraph>
            </BaseCard>
          </div>
        </div>
      </div>
    </div>

    <!--
      MESSAGES LIST
      Active chat interface showing conversation history
      Supports user and assistant messages with different styling
    -->
    <div v-else class="flex-1 overflow-y-auto px-4 py-8">
      <!--
        Message iteration with proper keys for performance
        Uses message.id when available, falls back to index
      -->
      <div v-for="(message, index) in messages" :key="message.id || index" class="mb-6">
        <!-- User messages: Right-aligned with primary color -->
        <div v-if="message.role === 'user'" class="flex justify-end">
          <div class="max-w-2xl bg-primary-500 text-white rounded-2xl px-4 py-3">
            {{ message.content }}
          </div>
        </div>
        <!-- Assistant messages: Left-aligned with muted background -->
        <div v-else class="flex justify-start">
          <div class="max-w-2xl bg-muted-100 dark:bg-muted-800 rounded-2xl px-4 py-3">
            {{ message.content }}
          </div>
        </div>
      </div>

      <!--
        LOADING INDICATOR
        Shown while AI is generating a response
        Provides visual feedback during streaming
      -->
      <div v-if="isLoading" class="flex justify-start mb-6">
        <div class="max-w-2xl bg-muted-100 dark:bg-muted-800 rounded-2xl px-4 py-3">
          <div class="flex items-center gap-2">
            <Icon name="svg-spinners:12-dots-scale-rotate" class="size-5 text-primary-500" />
            <span class="text-muted-600 dark:text-muted-300">AI is thinking...</span>
          </div>
        </div>
      </div>
    </div>

    <!--
      INPUT SECTION
      Fixed bottom section for message composition
      Includes textarea, attachment button, and send button
    -->
    <div class="shrink-0 pb-6">
      <div class="relative min-h-14">
        <!--
          MESSAGE INPUT TEXTAREA
          Auto-resizing textarea with keyboard support
          Disabled states provide clear visual feedback
        -->
        <textarea
          v-model="inputMessage"
          :placeholder="selectedModel ? 'Type your message...' : 'Select a model to start chatting'"
          :disabled="!selectedModel || isLoading"
          class="field-sizing-content placeholder:text-muted-300 dark:placeholder:text-muted-700 focus-visible:nui-focus bg-muted-100 dark:bg-muted-950 relative min-h-14 w-full rounded-4xl py-4.5 px-16 leading-tight resize-none disabled:opacity-50 disabled:cursor-not-allowed"
          @keydown="handleKeydown"
        />

        <!-- ATTACHMENT BUTTON (Future Feature) -->
        <BaseTooltip content="Attach a file">
          <button
            type="button"
            class="absolute bottom-1.5 start-0 flex size-14 items-center justify-center"
            :disabled="!selectedModel || isLoading"
          >
            <Icon name="solar:paperclip-linear" class="text-muted-500 size-6" />
          </button>
        </BaseTooltip>

        <!--
          SEND MESSAGE BUTTON
          Dynamic styling based on state (disabled/enabled)
          Shows loading spinner when message is being sent
        -->
        <button
          type="button"
          class="absolute bottom-1.5 end-0 flex size-14 items-center justify-center"
          :disabled="!selectedModel || isLoading || !inputMessage.trim()"
          @click="sendMessage"
        >
          <span
            class="flex size-10 items-center justify-center rounded-full"
            :class="[
              (!selectedModel || isLoading || !inputMessage.trim())
                ? 'bg-muted-300 dark:bg-muted-700'
                : 'bg-muted-900',
            ]"
          >
            <!-- Loading spinner during message sending -->
            <Icon
              v-if="isLoading"
              name="svg-spinners:12-dots-scale-rotate"
              class="size-5 text-white"
            />
            <!-- Send arrow icon when ready -->
            <Icon
              v-else
              name="mdi:arrow-up"
              class="size-5 text-white"
            />
          </span>
        </button>
      </div>

      <!-- DISCLAIMER TEXT -->
      <div class="mt-1 text-center">
        <BaseText size="xs" class="text-muted-400 dark:text-muted-500">
          AI may produce errors. Make sure to verify important info.
        </BaseText>
      </div>
    </div>
  </div>
</template>
