import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { getIntegrationWithApiKey } from '../../../server/utils/integrations'

// Mock Firestore Admin
const mockDoc = {
  get: vi.fn(),
}

const mockCollection = {
  doc: vi.fn(() => mockDoc),
}

const mockDb = {
  collection: vi.fn(() => mockCollection),
}

const mockGetFirestoreAdmin = vi.fn(() => mockDb)

// Mock decrypt function
const mockDecrypt = vi.fn()

// Mock environment
const originalEnv = process.env

vi.mock('../../../../auth-module/server/firebase/init', () => ({
  useFirebaseServer: vi.fn(() => Promise.resolve({ firestore: mockDb })),
}))

vi.mock('../../../../auth-module/server/utils/session', () => ({
  getUserSession: vi.fn(() => Promise.resolve({
    user: { token: { idToken: 'mock-token' } }
  })),
}))

vi.mock('firebase/firestore', () => ({
  doc: vi.fn((firestore, collection, id) => ({ collection, id })),
  getDoc: vi.fn(() => Promise.resolve({
    exists: () => true,
    data: () => mockDoc.get(),
  })),
}))

vi.mock('../../../../auth-module/utils/encryption', () => ({
  decrypt: mockDecrypt,
}))

describe('integrations', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    process.env = { ...originalEnv }
    process.env.NUXT_ENCRYPTION_KEY = 'test-encryption-key'

    // Mock console methods
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    process.env = originalEnv
    vi.restoreAllMocks()
  })

  describe('getIntegrationWithApiKey', () => {
    it('should successfully fetch and return integration with API key', async () => {
      const mockIntegrationData = {
        id: 'integration-1',
        name: 'Test Integration',
        provider: 'openai',
        isActive: true,
        credentials: {
          apiKey: 'test-api-key',
        },
      }

      mockDoc.get.mockResolvedValue({
        exists: true,
        id: 'integration-1',
        data: () => mockIntegrationData,
      })

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      expect(mockDb.collection).toHaveBeenCalledWith('integrations')
      expect(mockCollection.doc).toHaveBeenCalledWith('integration-1')
      expect(result).toEqual({
        id: 'integration-1',
        ...mockIntegrationData,
        credentials: {
          apiKey: 'test-api-key',
        },
      })
    })

    it('should return null if integration does not exist', async () => {
      mockDoc.get.mockResolvedValue({
        exists: false,
      })

      const result = await getIntegrationWithApiKey('non-existent', 'workspace-1')

      expect(result).toBe(null)
      expect(console.error).toHaveBeenCalledWith('Integration non-existent not found')
    })

    it('should return null if integration is not active', async () => {
      const mockIntegrationData = {
        isActive: false,
        credentials: {
          apiKey: 'test-api-key',
        },
      }

      mockDoc.get.mockResolvedValue({
        exists: true,
        id: 'integration-1',
        data: () => mockIntegrationData,
      })

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      expect(result).toBe(null)
      expect(console.error).toHaveBeenCalledWith('Integration integration-1 is not active')
    })

    it('should return null if integration has no API key', async () => {
      const mockIntegrationData = {
        isActive: true,
        credentials: {},
      }

      mockDoc.get.mockResolvedValue({
        exists: true,
        id: 'integration-1',
        data: () => mockIntegrationData,
      })

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      expect(result).toBe(null)
      expect(console.error).toHaveBeenCalledWith('Integration integration-1 has no API key')
    })

    it('should decrypt encrypted API key', async () => {
      const encryptedKey = 'very-long-encrypted-base64-string-that-needs-decryption'
      const decryptedKey = 'decrypted-api-key'

      const mockIntegrationData = {
        isActive: true,
        credentials: {
          apiKey: encryptedKey,
        },
      }

      mockDoc.get.mockResolvedValue({
        exists: true,
        id: 'integration-1',
        data: () => mockIntegrationData,
      })

      mockDecrypt.mockResolvedValue(decryptedKey)

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      expect(mockDecrypt).toHaveBeenCalledWith(encryptedKey, 'test-encryption-key')
      expect(result?.credentials.apiKey).toBe(decryptedKey)
      expect(console.log).toHaveBeenCalledWith('[Integration] Successfully decrypted API key')
    })

    it('should use original key if decryption fails', async () => {
      const originalKey = 'some-long-api-key-that-might-not-be-encrypted'

      const mockIntegrationData = {
        isActive: true,
        credentials: {
          apiKey: originalKey,
        },
      }

      mockDoc.get.mockResolvedValue({
        exists: true,
        id: 'integration-1',
        data: () => mockIntegrationData,
      })

      mockDecrypt.mockRejectedValue(new Error('Decryption failed'))

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      expect(mockDecrypt).toHaveBeenCalled()
      expect(result?.credentials.apiKey).toBe(originalKey)
      expect(console.log).toHaveBeenCalledWith(
        '[Integration] API key doesn\'t need decryption or decryption failed:',
        expect.any(Error),
      )
    })

    it('should not attempt decryption for short API keys', async () => {
      const shortKey = 'short-key'

      const mockIntegrationData = {
        isActive: true,
        credentials: {
          apiKey: shortKey,
        },
      }

      mockDoc.get.mockResolvedValue({
        exists: true,
        id: 'integration-1',
        data: () => mockIntegrationData,
      })

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      expect(mockDecrypt).not.toHaveBeenCalled()
      expect(result?.credentials.apiKey).toBe(shortKey)
    })

    it('should handle Firestore errors', async () => {
      mockDoc.get.mockRejectedValue(new Error('Firestore error'))

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      expect(result).toBe(null)
      expect(console.error).toHaveBeenCalledWith('Error fetching integration:', expect.any(Error))
    })

    it('should throw error if encryption key is not configured', async () => {
      delete process.env.NUXT_ENCRYPTION_KEY
      delete process.env.ENCRYPTION_KEY

      const encryptedKey = 'very-long-encrypted-base64-string-that-needs-decryption'

      const mockIntegrationData = {
        isActive: true,
        credentials: {
          apiKey: encryptedKey,
        },
      }

      mockDoc.get.mockResolvedValue({
        exists: true,
        id: 'integration-1',
        data: () => mockIntegrationData,
      })

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      // Should fail and return null due to missing encryption key
      expect(result).toBe(null)
      expect(console.error).toHaveBeenCalledWith('Error fetching integration:', expect.any(Error))
    })

    it('should use ENCRYPTION_KEY as fallback', async () => {
      delete process.env.NUXT_ENCRYPTION_KEY
      process.env.ENCRYPTION_KEY = 'fallback-encryption-key'

      const encryptedKey = 'very-long-encrypted-base64-string-that-needs-decryption'
      const decryptedKey = 'decrypted-api-key'

      const mockIntegrationData = {
        isActive: true,
        credentials: {
          apiKey: encryptedKey,
        },
      }

      mockDoc.get.mockResolvedValue({
        exists: true,
        id: 'integration-1',
        data: () => mockIntegrationData,
      })

      mockDecrypt.mockResolvedValue(decryptedKey)

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      expect(mockDecrypt).toHaveBeenCalledWith(encryptedKey, 'fallback-encryption-key')
      expect(result?.credentials.apiKey).toBe(decryptedKey)
    })

    it('should handle integration with missing credentials object', async () => {
      const mockIntegrationData = {
        isActive: true,
        // no credentials object
      }

      mockDoc.get.mockResolvedValue({
        exists: true,
        id: 'integration-1',
        data: () => mockIntegrationData,
      })

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      expect(result).toBe(null)
      expect(console.error).toHaveBeenCalledWith('Integration integration-1 has no API key')
    })

    it('should handle non-string API keys', async () => {
      const mockIntegrationData = {
        isActive: true,
        credentials: {
          apiKey: 12345, // non-string value
        },
      }

      mockDoc.get.mockResolvedValue({
        exists: true,
        id: 'integration-1',
        data: () => mockIntegrationData,
      })

      const result = await getIntegrationWithApiKey('integration-1', 'workspace-1')

      // Should not attempt decryption for non-string values
      expect(mockDecrypt).not.toHaveBeenCalled()
      expect(result?.credentials.apiKey).toBe(12345)
    })
  })
})
