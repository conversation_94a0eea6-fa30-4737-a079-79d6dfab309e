# AI Module Testing Guide

This directory contains comprehensive tests for the AI module, covering unit tests, integration tests, and end-to-end tests.

## Test Structure

```
tests/
├── setup.ts                    # Test setup and global mocks
├── fixtures/                   # Test data and mock objects
│   └── ai-data.ts
├── utils/                      # Test utilities and helpers
│   └── test-utils.ts
├── unit/                       # Unit tests
│   ├── composables/            # Tests for Vue composables
│   ├── components/             # Tests for Vue components
│   └── server/                 # Tests for server utilities
├── integration/                # Integration tests
│   └── api/                    # API endpoint tests
└── e2e/                        # End-to-end tests
    └── ai-chat.spec.ts
```

## Test Coverage

The test suite aims for **70%+ code coverage** across:

- **Composables**: `useAiChat`, `useAiConversations`, `useAiModels`
- **Components**: `AiChatInterface`, `AiDynamicRenderer`, `AiChatWorkspace`
- **Server Utilities**: Integration handling, MCP tools, conversation management
- **API Endpoints**: Chat streaming, conversation CRUD, WebSocket handling
- **Integration**: End-to-end chat functionality and artifact rendering

## Running Tests

### Unit Tests
```bash
# Run all unit tests
pnpm test:unit

# Run with watch mode
pnpm test:watch

# Run specific test file
pnpm test useAiChat.test.ts
```

### Integration Tests
```bash
# Run integration tests
pnpm test:integration

# Run specific integration test
pnpm test tests/integration/api/chat.test.ts
```

### End-to-End Tests
```bash
# Run E2E tests
pnpm test:e2e

# Run E2E tests in headed mode
pnpm test:e2e --headed

# Run specific E2E test
pnpm test:e2e ai-chat.spec.ts
```

### Coverage Reports
```bash
# Generate coverage report
pnpm test:coverage

# View coverage in browser
open coverage/index.html
```

### All Tests
```bash
# Run all tests
pnpm test
```

## Test Configuration

### Vitest Configuration
- **Environment**: happy-dom for browser simulation
- **Coverage**: v8 provider with HTML/JSON reports
- **Thresholds**: 70% minimum for statements, branches, functions, and lines
- **Setup**: Global mocks for Nuxt composables and external dependencies

### Playwright Configuration
- **Browsers**: Chromium, Firefox, WebKit
- **Base URL**: http://localhost:3000
- **Retries**: 2 retries in CI environment
- **Trace**: On first retry for debugging

## Mocking Strategy

### Global Mocks
- **Nuxt Composables**: `useAuth`, `useDataApi`, `$fetch`
- **AI SDK**: `useChat`, `streamText`, model providers
- **Firebase**: Firestore operations and auth
- **WebSocket**: Browser WebSocket API

### Test Utilities
- **createMockAuth**: Mock authentication state
- **createMockUseChat**: Mock AI SDK chat functionality
- **createMockFetch**: Mock HTTP requests
- **createMockWebSocket**: Mock WebSocket connections

## Writing Tests

### Unit Test Example
```typescript
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { useAiChat } from '../../../composables/useAiChat'
import { createMockAuth, createMockUseChat } from '../../utils/test-utils'

describe('useAiChat', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.useAuth = vi.fn(() => createMockAuth())
  })

  it('should send chat message', async () => {
    const { sendChatMessage } = useAiChat(mockConfig)
    await sendChatMessage('Hello!')

    expect(mockUseChat.handleSubmit).toHaveBeenCalled()
  })
})
```

### Component Test Example
```typescript
import { mount } from '@vue/test-utils'
import AiChatInterface from '../../../components/AiChatInterface.vue'

describe('AiChatInterface', () => {
  it('should render chat interface', () => {
    const wrapper = mount(AiChatInterface, {
      props: { selectedModel: 'gpt-4' }
    })

    expect(wrapper.find('textarea').exists()).toBe(true)
  })
})
```

### E2E Test Example
```typescript
import { expect, test } from '@playwright/test'

test('should send and receive messages', async ({ page }) => {
  await page.goto('/ai')
  await page.fill('textarea', 'Hello!')
  await page.press('textarea', 'Enter')

  await expect(page.locator('text=Hello!')).toBeVisible()
})
```

## Testing Best Practices

### 1. Mock External Dependencies
- Always mock API calls, Firebase, and external services
- Use consistent mock data from fixtures
- Reset mocks between tests

### 2. Test User Interactions
- Test click events, form submissions, keyboard interactions
- Verify UI state changes and feedback
- Test error handling and edge cases

### 3. Verify Integration Points
- Test component props and events
- Verify API request/response handling
- Test WebSocket message flow

### 4. Test Async Operations
- Use proper async/await patterns
- Test loading states and error handling
- Verify cleanup and resource management

### 5. Maintain Test Data
- Use fixtures for consistent test data
- Keep mock responses realistic
- Update tests when types change

## Debugging Tests

### Debug Individual Tests
```bash
# Run single test with debug output
pnpm test --reporter=verbose useAiChat.test.ts

# Debug with browser devtools (for component tests)
pnpm test:ui

# Debug E2E with browser
pnpm test:e2e --headed --debug
```

### Common Issues

1. **Mock not working**: Check import order and mock setup
2. **Component not rendering**: Verify global component stubs
3. **Async test failing**: Ensure proper await and error handling
4. **E2E test timing out**: Check selectors and mock responses

## CI/CD Integration

Tests run automatically on:
- Pull requests
- Main branch commits
- Release candidates

### GitHub Actions Example
```yaml
- name: Run Tests
  run: |
    pnpm test:unit
    pnpm test:integration
    pnpm test:e2e
    pnpm test:coverage
```

## Contributing

When adding new features:

1. **Write tests first** (TDD approach recommended)
2. **Maintain coverage** above 70% threshold
3. **Update fixtures** when adding new types
4. **Add E2E tests** for user-facing features
5. **Document test patterns** for complex scenarios

For questions or issues with tests, check the existing test files for patterns and examples.
