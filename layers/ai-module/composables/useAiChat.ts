/**
 * useAiChat - Core AI Chat Composable
 *
 * A comprehensive Vue 3 composable that provides full AI chat functionality
 * including message handling, conversation management, security validation,
 * artifact generation, and multi-model support.
 *
 * Features:
 * - Real-time streaming chat with AI models (OpenAI, Anthropic, Google, XAI)
 * - Security validation and rate limiting
 * - Conversation persistence and management
 * - Artifact generation from tool calls (weather, documents, code blocks)
 * - Message sanitization and content filtering
 * - Usage metrics and cost tracking
 * - Error handling and recovery
 *
 * Architecture:
 * - Built on top of @ai-sdk/vue for streaming capabilities
 * - Integrates with auth system for user/workspace context
 * - Uses conversation API for persistence
 * - Implements comprehensive security layer
 *
 * Performance Considerations:
 * - Lazy artifact creation to reduce memory usage
 * - Optimized message serialization for large conversations
 * - Rate limiting to prevent API abuse
 * - Efficient state management with reactive refs
 *
 * Security Features:
 * - Input validation and sanitization
 * - Rate limiting (5 messages per minute per user)
 * - CSRF token validation
 * - Content filtering for malicious inputs
 *
 * @param {AiChatConfig} initialConfig - Initial configuration for the chat system
 * @returns {object} Chat composable interface with methods and reactive state
 *
 * @example
 * ```typescript
 * const chatConfig = {
 *   integrationId: 'integration-id',
 *   model: 'gemini-1.5-pro',
 *   provider: 'google',
 *   temperature: 0.7,
 *   maxTokens: 4000
 * }
 *
 * const chat = useAiChat(chatConfig)
 *
 * // Send a message
 * await chat.sendChatMessage('Hello, how can you help me?')
 *
 * // Access reactive state
 * console.log(chat.messages.value) // Current messages
 * console.log(chat.isLoading.value) // Loading state
 * console.log(chat.artifacts.value) // Generated artifacts
 * ```
 */
import type {
  AiArtifact,
  AiArtifactType,
  AiChatConfig,
  AiMessage,
} from '../types/ai'
import { useChat } from '@ai-sdk/vue'
import { computed, ref } from 'vue'

/**
 * Main AI Chat Composable Function
 *
 * Creates a reactive chat interface with comprehensive functionality
 * for AI interactions, security, and conversation management.
 *
 * @param {AiChatConfig} initialConfig - Initial chat configuration including model and provider settings
 */
export function useAiChat(initialConfig: AiChatConfig) {
  const { user, currentWorkspace, isAuthenticated } = useAuth()
  const { addMessageToConversation } = useAiConversations()
  const security = useSecurity()

  // Security state
  const securityValidation = ref<{ isValid: boolean, issues: string[] }>({ isValid: true, issues: [] })

  // State for artifacts
  const artifacts = ref<AiArtifact[]>([])
  const expandedArtifactId = ref<string>()

  // Chat state
  const conversationId = ref<string | null>(null)
  const metrics = ref<any>()

  // Configuration state - make it reactive
  const config = ref<AiChatConfig>(initialConfig)

  // Track if configuration is valid
  const isConfigValid = computed(() =>
    config.value.integrationId
    && config.value.model
    && config.value.provider,
  )

  // Use AI SDK's useChat composable - always initialize but disable when config invalid
  const {
    messages,
    input,
    handleSubmit,
    isLoading,
    error,
    reload,
    stop,
    setMessages,
  } = useChat({
    api: '/api/ai/chat',
    maxSteps: 5,
    body: computed(() => ({
      config: config.value,
      conversationId: conversationId.value,
    })),
    onToolCall: ({ toolCall }) => {
      // Handle tool calls and convert to artifacts
      if (toolCall.result && typeof toolCall.result === 'object') {
        const result = toolCall.result as any
        const artifact: AiArtifact = {
          id: `artifact-${Date.now()}`,
          type: result.type as AiArtifactType,
          title: result.title || 'Untitled',
          content: result,
        }
        artifacts.value.push(artifact)
      }
    },
    onFinish: (message, { usage }) => {
      // Update metrics
      if (usage) {
        metrics.value = {
          promptTokens: usage.promptTokens,
          completionTokens: usage.completionTokens,
          totalTokens: usage.totalTokens,
        }
      }

      // Save to conversation if exists
      if (conversationId.value && message) {
        const aiMessage: AiMessage = {
          id: message.id,
          role: message.role as 'user' | 'assistant',
          content: message.content,
          timestamp: new Date(message.createdAt || Date.now()),
          workspaceId: currentWorkspace.value!.id,
          conversationId: conversationId.value,
          model: config.value.model,
          artifacts: artifacts.value.length > 0 ? [...artifacts.value] : undefined,
        }
        addMessageToConversation(conversationId.value, aiMessage)
      }
    },
  })

  // Send chat message (wrapper around handleSubmit)
  const sendChatMessage = async (content: string) => {
    if (!content.trim())
      return

    // Check if configuration is valid
    if (!isConfigValid.value) {
      throw new Error('Invalid chat configuration. Please check your model selection.')
    }

    // Security validation
    const validation = security.validateMessage(content)
    securityValidation.value = validation

    if (!validation.isValid) {
      security.logSecurityEvent({
        type: 'validation',
        severity: 'medium',
        message: `Message validation failed: ${validation.issues.join(', ')}`,
      })
      throw new Error(`Message validation failed: ${validation.issues.join(', ')}`)
    }

    // Rate limiting check (5 messages per minute)
    const userId = user.value?.id || 'anonymous'
    if (!security.checkRateLimit(`chat-${userId}`, 5, 60000)) {
      throw new Error('Rate limit exceeded. Please wait before sending another message.')
    }

    // Sanitize content
    const sanitizedContent = security.sanitizeMessage(content)

    // Create a conversation if one doesn't exist
    if (!conversationId.value) {
      try {
        const title = sanitizedContent.slice(0, 50) + (sanitizedContent.length > 50 ? '...' : '')
        await createConversation(title)
      }
      catch (error) {
        console.error('Failed to create conversation:', error)
        throw new Error('Failed to create conversation')
      }
    }

    // Clear artifacts for new message
    artifacts.value = []

    // Set input and submit with sanitized content
    input.value = sanitizedContent
    await handleSubmit()
  }

  // Create a new conversation
  const createConversation = async (title?: string) => {
    // Validate title if provided
    if (title) {
      const titleValidation = security.validateMessage(title)
      if (!titleValidation.isValid) {
        throw new Error(`Invalid conversation title: ${titleValidation.issues.join(', ')}`)
      }
      title = security.sanitizeMessage(title)
    }

    const response = await $fetch('/api/ai/conversations', {
      method: 'POST',
      headers: {
        'X-CSRF-Token': security.getCsrfToken(),
      },
      body: {
        title: title || 'New Conversation',
        workspaceId: currentWorkspace.value?.id,
        userId: user.value?.id,
        metadata: {
          provider: config.value.provider,
          model: config.value.model,
        },
      },
    })
    conversationId.value = response.id
    return response
  }

  // Load conversation history
  const loadConversation = async (id: string) => {
    const response = await $fetch(`/api/ai/conversations/${id}`, {
      query: {
        workspaceId: currentWorkspace.value?.id,
        userId: user.value?.id,
      },
    })

    if (response && response.messages) {
      // Convert stored messages to AI SDK format
      const sdkMessages = response.messages.map((msg: AiMessage) => ({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        createdAt: new Date(msg.timestamp),
      }))
      setMessages(sdkMessages)
      conversationId.value = id
    }

    return response
  }

  // Clear messages
  const clearMessages = () => {
    setMessages([])
    artifacts.value = []
    metrics.value = undefined
  }

  // Set conversation ID
  const setConversationId = (id: string | null) => {
    conversationId.value = id
  }

  // Update configuration
  const updateConfig = (newConfig: Partial<AiChatConfig>) => {
    config.value = { ...config.value, ...newConfig }
  }

  return {
    // State from AI SDK
    messages: computed(() => messages.value),
    input,
    isLoading,
    error,

    // Custom state
    conversationId: computed(() => conversationId.value),
    metrics: computed(() => metrics.value),
    artifacts: computed(() => artifacts.value),
    expandedArtifactId,
    isConfigValid,

    // Security state
    securityValidation: computed(() => securityValidation.value),
    securityEvents: security.securityEvents,
    isRateLimited: security.isRateLimited,

    // Methods from AI SDK
    reload,
    stop,

    // Custom methods
    sendChatMessage,
    clearMessages,
    setConversationId,
    createConversation,
    loadConversation,
    updateConfig,

    // Security methods
    validateMessage: security.validateMessage,
    sanitizeMessage: security.sanitizeMessage,
    validateFileUpload: security.validateFileUpload,
    validateUrl: security.validateUrl,
    clearSecurityEvents: security.clearSecurityEvents,
    getSecurityEvents: security.getSecurityEvents,
  }
}
