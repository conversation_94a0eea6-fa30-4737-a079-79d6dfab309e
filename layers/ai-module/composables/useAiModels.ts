/**
 * useAiModels - AI Models Management Composable
 *
 * A comprehensive Vue 3 composable that manages AI model discovery, configuration,
 * and availability based on workspace integrations. Provides reactive access to
 * available AI models from multiple providers with proper authentication and filtering.
 *
 * Features:
 * - Multi-provider AI model support (OpenAI, Anthropic, Google, XAI, etc.)
 * - Workspace-scoped model availability based on integrations
 * - Reactive model list with automatic updates
 * - Provider-grouped model organization
 * - Integration-based model filtering and mapping
 * - Authentication state management
 * - Error handling and loading states
 * - Model lookup by ID functionality
 *
 * Architecture:
 * - Integrates with central AI integrations system
 * - Uses reactive Vue composition for state management
 * - Watches workspace and authentication changes
 * - Provides readonly state exposure for data integrity
 * - Implements efficient model mapping and grouping
 *
 * Model Providers Supported:
 * - OpenAI: GPT-4 Turbo, GPT-4, GPT-3.5 Turbo
 * - Anthropic: Claude 3 Opus, Sonnet, Haiku
 * - Google: Gemini 1.5 Pro/Flash, Gemini 2.0 Flash
 * - XAI: Grok Beta, Grok 2, Grok 2 Mini
 * - Mistral: Large, Medium, Small variants
 * - Groq: Llama 3.1 models, Mixtral
 *
 * Performance Considerations:
 * - Lazy loading based on authentication state
 * - Efficient model mapping with provider grouping
 * - Readonly state exposure to prevent mutations
 * - Centralized loading state management
 * - Optimized reactivity with computed properties
 *
 * Security Features:
 * - Workspace-scoped model access
 * - Authentication-based model filtering
 * - Integration validation and error handling
 * - Safe model configuration exposure
 *
 * @returns {object} AI models composable interface
 *
 * @example
 * const { availableModels, modelsByProvider, loading, getModelById } = useAiModels()
 *
 * // Access available models reactively
 * console.log(availableModels.value) // Array of available AI models
 *
 * // Get models grouped by provider
 * console.log(modelsByProvider.value.openai) // OpenAI models only
 *
 * // Find specific model
 * const model = getModelById('integration-123-gpt-4-turbo')
 */
import type { AiModel } from '../types/ai'
import { computed, readonly, ref, watch } from 'vue'
import { useAIIntegrations } from '../../auth-module/composables/useAppIntegrations'

export function useAiModels() {
  const { currentWorkspace, user, isAuthenticated } = useAuth()
  // Use central AI integrations system for optimal performance
  const { integrations: aiIntegrations, loading: integrationsLoading } = useAIIntegrations()

  // ===== REACTIVE STATE =====
  /**
   * Available AI models for the current workspace
   * Contains processed models from active integrations with proper IDs
   * @type {Ref<AiModel[]>}
   */
  const availableModels = ref<AiModel[]>([])

  /**
   * Loading state for model processing operations
   * Tracks local model mapping and configuration processing
   * @type {Ref<boolean>}
   */
  const loading = ref(false)

  /**
   * Error state for model operations
   * Contains user-friendly error messages for display
   * @type {Ref<string | null>}
   */
  const error = ref<string | null>(null)

  // ===== COMPUTED PROPERTIES =====
  /**
   * Combined loading state for comprehensive UI feedback
   *
   * Combines local model processing loading with central integrations
   * loading to provide accurate loading state to consumers.
   *
   * @returns {boolean} True if any loading operation is in progress
   */
  const isLoading = computed(() => loading.value || integrationsLoading.value)

  /**
   * Models grouped by AI provider for organized display
   *
   * Transforms the flat array of available models into a grouped
   * object where keys are provider names and values are arrays
   * of models for that provider.
   *
   * Complexity: O(n) where n is the number of available models
   *
   * @returns {Record<string, AiModel[]>} Models grouped by provider
   *
   * @example
   * {
   *   openai: [{ model: 'gpt-4', ... }, { model: 'gpt-3.5-turbo', ... }],
   *   anthropic: [{ model: 'claude-3-opus', ... }],
   *   google: [{ model: 'gemini-1.5-pro', ... }]
   * }
   */
  const modelsByProvider = computed(() => {
    const grouped: Record<string, AiModel[]> = {}
    availableModels.value.forEach((model) => {
      if (!grouped[model.provider]) {
        grouped[model.provider] = []
      }
      grouped[model.provider].push(model)
    })
    return grouped
  })

  // ===== CORE FUNCTIONS =====
  /**
   * Fetch and process available AI models for the current workspace
   *
   * Processes AI integrations from the central system and maps them to
   * available models with proper configuration. Handles authentication,
   * workspace validation, and error states.
   *
   * Flow:
   * 1. Validates authentication and workspace state
   * 2. Processes active AI integrations from central system
   * 3. Maps each integration to provider-specific models
   * 4. Creates unique model IDs combining integration and model
   * 5. Updates reactive state with processed models
   *
   * Error Handling:
   * - Returns early if not authenticated or no workspace
   * - Handles empty integrations gracefully
   * - Catches and logs processing errors
   * - Updates error state for UI feedback
   *
   * Performance:
   * - Uses central integrations system (already filtered)
   * - Efficient model mapping with provider lookup
   * - Proper loading state management
   *
   * Complexity: O(n*m) where n is integrations and m is models per provider
   *
   * @returns {void}
   *
   * @example
   * fetchAvailableModels() // Processes workspace integrations to available models
   */
  const fetchAvailableModels = () => {
    if (!isAuthenticated.value || !currentWorkspace.value) {
      error.value = 'No workspace selected'
      availableModels.value = []
      return
    }

    loading.value = true
    error.value = null

    try {
      // Use AI integrations directly from central system (already filtered by category and active status)
      console.log('Processing AI integrations for workspace:', currentWorkspace.value.id)
      console.log('AI integrations from central system:', aiIntegrations.value)

      if (!aiIntegrations.value || aiIntegrations.value.length === 0) {
        console.log('No AI integrations found - showing empty state')
        availableModels.value = []
        return
      }

      // Map integrations to available models
      const models: AiModel[] = []

      aiIntegrations.value.forEach((integration: any) => {
        const provider = integration.provider
        const providerModels = getProviderModels(provider)

        providerModels.forEach((model) => {
          models.push({
            ...model,
            id: `${integration.id}-${model.model}`,
            provider,
            integrationId: integration.id,
          })
        })
      })

      availableModels.value = models
      console.log('Final available models from central AI integrations:', models)
    }
    catch (err) {
      console.error('Error processing available models:', err)
      error.value = 'Failed to process available models'
    }
    finally {
      loading.value = false
    }
  }

  /**
   * Get available models for a specific AI provider
   *
   * Returns the model configurations available for a given provider.
   * Each provider has a predefined set of models with their capabilities,
   * context windows, and display names.
   *
   * Supported Providers:
   * - openai: GPT-4 variants and GPT-3.5 Turbo
   * - anthropic: Claude 3 family (Opus, Sonnet, Haiku)
   * - google: Gemini 1.5/2.0 Pro and Flash variants
   * - xai: Grok models (Beta, 2, 2 Mini)
   * - mistral: Large, Medium, Small variants
   * - groq: Llama 3.1 and Mixtral models
   *
   * Model Configuration:
   * - model: API model identifier
   * - name: Human-readable display name
   * - contextWindow: Maximum context length in tokens
   * - capabilities: Array of supported features (chat, vision)
   *
   * Complexity: O(1) - Direct object lookup
   *
   * @param {string} provider - The AI provider name (lowercase)
   * @returns {Partial<AiModel>[]} Array of model configurations for the provider
   *
   * @example
   * getProviderModels('openai') // Returns OpenAI model configurations
   * getProviderModels('unknown') // Returns empty array
   */
  const getProviderModels = (provider: string): Partial<AiModel>[] => {
    const modelConfigs: Record<string, Partial<AiModel>[]> = {
      openai: [
        { model: 'gpt-4-turbo', name: 'GPT-4 Turbo', contextWindow: 128000, capabilities: ['chat', 'vision'] },
        { model: 'gpt-4', name: 'GPT-4', contextWindow: 8192, capabilities: ['chat'] },
        { model: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', contextWindow: 16385, capabilities: ['chat'] },
      ],
      anthropic: [
        { model: 'claude-3-opus-20240229', name: 'Claude 3 Opus', contextWindow: 200000, capabilities: ['chat', 'vision'] },
        { model: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', contextWindow: 200000, capabilities: ['chat', 'vision'] },
        { model: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', contextWindow: 200000, capabilities: ['chat', 'vision'] },
      ],
      google: [
        { model: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', contextWindow: 2000000, capabilities: ['chat', 'vision'] },
        { model: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', contextWindow: 1000000, capabilities: ['chat', 'vision'] },
        { model: 'gemini-2.0-flash-exp', name: 'Gemini 2.0 Flash (Experimental)', contextWindow: 1000000, capabilities: ['chat', 'vision'] },
        { model: 'gemini-1.5-pro-002', name: 'Gemini 1.5 Pro 002', contextWindow: 2000000, capabilities: ['chat', 'vision'] },
        { model: 'gemini-1.5-flash-002', name: 'Gemini 1.5 Flash 002', contextWindow: 1000000, capabilities: ['chat', 'vision'] },
      ],
      mistral: [
        { model: 'mistral-large-latest', name: 'Mistral Large', contextWindow: 32000, capabilities: ['chat'] },
        { model: 'mistral-medium-latest', name: 'Mistral Medium', contextWindow: 32000, capabilities: ['chat'] },
        { model: 'mistral-small-latest', name: 'Mistral Small', contextWindow: 32000, capabilities: ['chat'] },
      ],
      groq: [
        { model: 'llama-3.1-70b-versatile', name: 'Llama 3.1 70B', contextWindow: 131072, capabilities: ['chat'] },
        { model: 'llama-3.1-8b-instant', name: 'Llama 3.1 8B', contextWindow: 131072, capabilities: ['chat'] },
        { model: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B', contextWindow: 32768, capabilities: ['chat'] },
      ],
      xai: [
        { model: 'grok-beta', name: 'Grok Beta', contextWindow: 131072, capabilities: ['chat'] },
        { model: 'grok-2', name: 'Grok 2', contextWindow: 131072, capabilities: ['chat'] },
        { model: 'grok-2-mini', name: 'Grok 2 Mini', contextWindow: 131072, capabilities: ['chat'] },
      ],
    }

    return modelConfigs[provider] || []
  }

  /**
   * Find a specific AI model by its unique ID
   *
   * Searches through the available models to find a model with the
   * specified ID. Model IDs are generated by combining the integration
   * ID with the model identifier (e.g., "integration-123-gpt-4-turbo").
   *
   * Use Cases:
   * - Model selection validation
   * - Configuration lookup for specific models
   * - Chat system model resolution
   * - Model capability checking
   *
   * Complexity: O(n) where n is the number of available models
   *
   * @param {string} modelId - The unique model ID to search for
   * @returns {AiModel | undefined} The found model or undefined if not found
   *
   * @example
   * const model = getModelById('integration-123-gpt-4-turbo')
   * if (model) {
   *   console.log(`Model: ${model.name}, Provider: ${model.provider}`)
   * }
   */
  const getModelById = (modelId: string): AiModel | undefined => {
    return availableModels.value.find(model => model.id === modelId)
  }

  // ===== REACTIVE EFFECTS =====
  /**
   * Watch for changes in AI integrations or workspace context
   *
   * Automatically refreshes available models when:
   * - AI integrations are updated (new/removed/modified)
   * - Current workspace changes
   * - Component mounts (immediate: true)
   *
   * This ensures the model list stays synchronized with the
   * current integration state and workspace context.
   */
  watch([aiIntegrations, currentWorkspace], () => {
    fetchAvailableModels()
  }, { immediate: true })

  /**
   * Watch for authentication state changes
   *
   * Handles authentication flow by:
   * - Fetching models when user authenticates
   * - Clearing models when user logs out
   *
   * Ensures proper cleanup and initialization based on
   * authentication state transitions.
   */
  watch(isAuthenticated, (authenticated) => {
    if (authenticated) {
      fetchAvailableModels()
    }
    else {
      availableModels.value = []
    }
  })

  return {
    availableModels: readonly(availableModels),
    modelsByProvider: readonly(modelsByProvider),
    loading: readonly(isLoading), // Use combined loading state
    error: readonly(error),
    fetchAvailableModels,
    getModelById,
  }
}
