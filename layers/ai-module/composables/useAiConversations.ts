import type { AiConversation, AiMessage } from '../types/ai'
import { computed, readonly, ref, watchEffect } from 'vue'

export function useAiConversations() {
  const { user, currentWorkspace, isAuthenticated } = useAuth()
  const { getAll, create, update, remove, getById } = useDataApi().useEntityApi<AiConversation>('ai_conversations')

  const conversations = ref<AiConversation[]>([])
  const currentConversation = ref<AiConversation | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  const conversationsByDate = computed(() => {
    const grouped: Record<string, AiConversation[]> = {
      'Today': [],
      'Last 7 days': [],
      'Last 30 days': [],
      'Older': [],
    }

    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

    conversations.value.forEach((conv) => {
      const convDate = new Date(conv.updatedAt)
      if (convDate >= today) {
        grouped.Today.push(conv)
      }
      else if (convDate >= sevenDaysAgo) {
        grouped['Last 7 days'].push(conv)
      }
      else if (convDate >= thirtyDaysAgo) {
        grouped['Last 30 days'].push(conv)
      }
      else {
        grouped.Older.push(conv)
      }
    })

    // Remove empty groups
    Object.keys(grouped).forEach((key) => {
      if (grouped[key].length === 0) {
        delete grouped[key]
      }
    })

    return grouped
  })

  const fetchConversations = async () => {
    if (!isAuthenticated.value || !user.value?.id || !currentWorkspace.value?.id) {
      error.value = 'No workspace or user selected'
      conversations.value = []
      return
    }

    loading.value = true
    error.value = null

    try {
      const data = await getAll({
        workspaceId: currentWorkspace.value.id,
        additionalFilters: [
          ['userId', '==', user.value.id],
        ],
        orderBy: 'updatedAt',
        orderDirection: 'desc',
      })

      conversations.value = data || []
    }
    catch (err) {
      console.error('Error fetching conversations:', err)
      error.value = 'Failed to fetch conversations'
    }
    finally {
      loading.value = false
    }
  }

  const createConversation = async (
    title: string,
    model: string,
    provider: string,
    integrationId: string,
    firstMessage?: AiMessage,
  ): Promise<AiConversation | null> => {
    if (!isAuthenticated.value || !user.value?.id || !currentWorkspace.value?.id) {
      error.value = 'No workspace or user selected'
      return null
    }

    loading.value = true
    error.value = null

    try {
      const conversationData: Partial<AiConversation> = {
        title,
        workspaceId: currentWorkspace.value.id,
        userId: user.value.id,
        model,
        provider,
        integrationId,
        messages: firstMessage ? [firstMessage] : [],
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const data = await create(conversationData, {
        workspaceId: currentWorkspace.value.id,
      })

      if (data) {
        conversations.value.unshift(data)
        currentConversation.value = data
        return data
      }
      return null
    }
    catch (err) {
      console.error('Error creating conversation:', err)
      error.value = 'Failed to create conversation'
      return null
    }
    finally {
      loading.value = false
    }
  }

  const updateConversation = async (
    conversationId: string,
    updates: Partial<AiConversation>,
  ) => {
    loading.value = true
    error.value = null

    try {
      const data = await update(conversationId, {
        ...updates,
        updatedAt: new Date(),
      })

      if (data) {
        const index = conversations.value.findIndex(c => c.id === conversationId)
        if (index !== -1) {
          conversations.value[index] = data
        }
        if (currentConversation.value?.id === conversationId) {
          currentConversation.value = data
        }
      }
    }
    catch (err) {
      console.error('Error updating conversation:', err)
      error.value = 'Failed to update conversation'
    }
    finally {
      loading.value = false
    }
  }

  const deleteConversation = async (conversationId: string) => {
    loading.value = true
    error.value = null

    try {
      await remove(conversationId)

      conversations.value = conversations.value.filter(c => c.id !== conversationId)
      if (currentConversation.value?.id === conversationId) {
        currentConversation.value = null
      }
    }
    catch (err) {
      console.error('Error deleting conversation:', err)
      error.value = 'Failed to delete conversation'
    }
    finally {
      loading.value = false
    }
  }

  const loadConversation = async (conversationId: string) => {
    loading.value = true
    error.value = null

    try {
      const data = await getById(conversationId)

      if (data) {
        currentConversation.value = data
        return data
      }
      return null
    }
    catch (err) {
      console.error('Error loading conversation:', err)
      error.value = 'Failed to load conversation'
      return null
    }
    finally {
      loading.value = false
    }
  }

  const addMessageToConversation = async (
    conversationId: string,
    message: AiMessage,
  ) => {
    if (!currentConversation.value || currentConversation.value.id !== conversationId) {
      await loadConversation(conversationId)
    }

    if (currentConversation.value) {
      const updatedMessages = [...currentConversation.value.messages, message]
      await updateConversation(conversationId, { messages: updatedMessages })
    }
  }

  // Initialize when user and workspace are available
  watchEffect(() => {
    if (isAuthenticated.value && user.value?.id && currentWorkspace.value?.id) {
      fetchConversations()
    }
    else {
      conversations.value = []
      currentConversation.value = null
    }
  })

  return {
    conversations: readonly(conversations),
    conversationsByDate: readonly(conversationsByDate),
    currentConversation: readonly(currentConversation),
    loading: readonly(loading),
    error: readonly(error),
    fetchConversations,
    createConversation,
    updateConversation,
    deleteConversation,
    loadConversation,
    addMessageToConversation,
  }
}
