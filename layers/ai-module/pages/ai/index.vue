<script setup lang="ts">
definePageMeta({
  layout: 'empty',
  title: 'AI Chat',
  preview: {
    title: 'AI Interface',
    description: 'For AI chat and conversation',
    categories: ['layouts', 'AI interface'],
    src: '/img/screens/layouts-ai.png',
    srcDark: '/img/screens/layouts-ai-dark.png',
    order: 0,
    new: true,
  },
})

const isOpen = ref(true)
const isMobileOpen = ref(false)
const isEphemeral = ref(false)
const currentConversationId = ref<string | null>(null)

// Model selection state
const selectedModel = ref<string>('')
const { availableModels, loading: modelsLoading } = useAiModels()

function handleNewChat() {
  currentConversationId.value = null
}

function handleSelectConversation(conversationId: string) {
  currentConversationId.value = conversationId
  isMobileOpen.value = false
}

// Watch for first available model to auto-select
watch(availableModels, (models) => {
  if (models.length > 0 && !selectedModel.value) {
    selectedModel.value = models[0].id
  }
}, { immediate: true })
</script>

<template>
  <div class="bg-muted-white dark:bg-muted-900 h-screen w-full overflow-hidden">
    <!-- Sidebar -->
    <div
      class="bg-muted-100 dark:bg-muted-950 fixed start-0 top-0 z-40 flex h-full w-[260px] flex-col transition-transform duration-300"
      :class="[
        isOpen ? 'lg:translate-x-0' : 'lg:-translate-x-full!',
        isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0',
      ]"
    >
      <div class="flex h-16 shrink-0 items-center gap-2 px-4">
        <div class="lg:hidden">
          <button
            type="button"
            class="nui-mask nui-mask-blob hover:bg-muted-200 dark:hover:bg-muted-800 flex size-10 items-center justify-center transition-colors duration-300"
            @click="isMobileOpen = false"
          >
            <Icon name="lucide:x" class="size-5" />
          </button>
        </div>
        <BaseTooltip content="Close Sidebar">
          <button
            type="button"
            class="nui-mask nui-mask-blob hover:bg-muted-200 dark:hover:bg-muted-800 hidden lg:flex size-10 items-center justify-center transition-colors duration-300"
            @click="isOpen = false"
          >
            <Icon name="solar:siderbar-linear" class="size-5" />
          </button>
        </BaseTooltip>
        <BaseTooltip content="New Chat">
          <button
            type="button"
            class="nui-mask nui-mask-blob hover:bg-muted-200 dark:hover:bg-muted-800 flex size-10 ms-auto items-center justify-center transition-colors duration-300"
            @click="handleNewChat"
          >
            <Icon name="lucide:plus" class="size-5" />
          </button>
        </BaseTooltip>
      </div>
      <div class="nui-slimscroll grow overflow-y-auto px-4 py-3">
        <div>
          <ul class="space-y-1">
            <li>
              <button type="button" class="hover:bg-muted-200 dark:hover:bg-muted-800 flex w-full items-center gap-2 rounded-xl p-1 transition-colors duration-300">
                <span class="dark:bg-muted-950 border-muted-300 dark:border-muted-800 flex size-8 items-center justify-center rounded-lg border bg-white">
                  <span class="bg-muted-100 dark:bg-muted-800 flex size-6 items-center justify-center rounded-md">
                    <TairoLogo class="text-primary-500 size-4" />
                  </span>
                </span>
                <BaseText size="sm" weight="medium">
                  Tairo GPT
                </BaseText>
              </button>
            </li>
            <li>
              <button type="button" class="hover:bg-muted-200 dark:hover:bg-muted-800 flex w-full items-center gap-2 rounded-xl p-1 transition-colors duration-300">
                <span class="dark:bg-muted-950 border-muted-300 dark:border-muted-800 flex size-8 items-center justify-center rounded-lg border bg-white">
                  <span class="bg-muted-100 dark:bg-muted-800 flex size-6 items-center justify-center rounded-md">
                    <Icon name="solar:graph-linear" class="text-primary-500 size-5" />
                  </span>
                </span>
                <BaseText size="sm" weight="medium">
                  Image Generator
                </BaseText>
              </button>
            </li>
            <li>
              <button type="button" class="hover:bg-muted-200 dark:hover:bg-muted-800 flex w-full items-center gap-2 rounded-xl p-1 transition-colors duration-300">
                <span class="dark:bg-muted-950 border-muted-300 dark:border-muted-800 flex size-8 items-center justify-center rounded-lg border bg-white">
                  <span class="bg-muted-100 dark:bg-muted-800 flex size-6 items-center justify-center rounded-md">
                    <Icon name="solar:graph-linear" class="text-primary-500 size-5" />
                  </span>
                </span>
                <BaseText size="sm" weight="medium">
                  Explore GPTs
                </BaseText>
              </button>
            </li>
          </ul>
          <!-- Conversations List -->
          <div class="mt-16">
            <AiConversationSidebar
              :current-conversation-id="currentConversationId"
              @select="handleSelectConversation"
              @new="handleNewChat"
            />
          </div>
        </div>
      </div>
      <div class="flex h-16 shrink-0 items-center gap-2 px-4">
        <BaseButton
          variant="dark"
          size="md"
          class="w-full"
        >
          Upgrade to Pro
        </BaseButton>
      </div>
    </div>
    <!-- Main content -->
    <div class="h-screen transition-all duration-300" :class="isOpen ? 'lg:ml-[260px]' : 'ml-0'">
      <div class="h-screen flex flex-col">
        <!-- Navigation -->
        <div class="shrink-0">
          <div class="flex h-16 w-full items-center justify-between px-2">
            <div class="flex items-center gap-2">
              <button
                v-if="!isOpen"
                type="button"
                class="nui-mask nui-mask-blob hover:bg-muted-100 dark:hover:bg-muted-800 flex size-10 items-center justify-center transition-colors duration-300"
                @click="isMobileOpen = true"
              >
                <Icon name="lucide:menu" class="size-5" />
              </button>
              <div class="w-40 md:w-48">
                <AiModelSelector
                  v-model="selectedModel"
                  :models="availableModels"
                  :loading="modelsLoading"
                />
              </div>
              <BaseTooltip content="Open Sidebar">
                <button
                  v-if="!isOpen"
                  type="button"
                  class="nui-mask nui-mask-blob hover:bg-muted-100 dark:hover:bg-muted-800 flex size-10 items-center justify-center transition-colors duration-300"
                  @click="isOpen = true"
                >
                  <Icon name="solar:siderbar-linear" class="size-5" />
                </button>
              </BaseTooltip>
            </div>
            <div class="flex items-center gap-2">
              <div class="scale-90">
                <BaseThemeSwitch />
              </div>
              <div>
                <BaseButton size="sm" rounded="full">
                  <Icon name="lucide:share-2" class="size-4" />
                  <span>Share</span>
                </BaseButton>
              </div>
              <AccountMenu horizontal />
            </div>
          </div>
        </div>
        <!-- Chat content area -->
        <div class="flex-1 overflow-hidden">
          <div class="mx-auto h-full w-full max-w-3xl px-4">
            <AiChatInterface
              :conversation-id="currentConversationId"
              :selected-model="selectedModel"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- Overlay -->
    <div
      role="button"
      tabindex="0"
      class="bg-muted-950/80 fixed start-0 top-0 z-[39] size-full transition-opacity duration-300 lg:pointer-events-none! lg:opacity-0!"
      :class="isMobileOpen ? 'opacity-100 pointer-events-all' : 'opacity-0 pointer-events-none'"
      @click="isMobileOpen = false"
    />
  </div>
</template>
