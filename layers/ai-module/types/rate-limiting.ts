export interface RateLimitConfig {
  keyPrefix: string
  maxRequests: number
  windowMs: number
  blockDurationMs?: number
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}

export interface RateLimitKey {
  userId?: string
  workspaceId?: string
  ip?: string
  endpoint?: string
  provider?: string
}

export interface RateLimitResult {
  allowed: boolean
  remainingPoints: number
  totalUsed: number
  resetTime: Date
  retryAfter: number
}

export interface CostConfig {
  provider: string
  model: string
  inputCostPer1k: number
  outputCostPer1k: number
  requestBaseCost?: number
}

export interface UsageMetrics {
  promptTokens: number
  completionTokens: number
  totalTokens: number
  cost: number
  requestCount: number
  timestamp: Date
  provider: string
  model: string
  userId: string
  workspaceId: string
  endpoint: string
}

export interface UsageLimits {
  workspaceId: string
  dailyTokenLimit?: number
  monthlyTokenLimit?: number
  dailyCostLimit?: number // in cents
  monthlyCostLimit?: number // in cents
  dailyRequestLimit?: number
  monthlyRequestLimit?: number
  perUserLimits?: {
    dailyTokenLimit?: number
    monthlyTokenLimit?: number
    dailyRequestLimit?: number
    monthlyRequestLimit?: number
  }
}

export interface UsageStats {
  period: 'daily' | 'monthly'
  totalTokens: number
  totalCost: number // in cents
  totalRequests: number
  byProvider: Record<string, {
    tokens: number
    cost: number
    requests: number
  }>
  byUser: Record<string, {
    tokens: number
    cost: number
    requests: number
  }>
  resetTime: Date
}

export interface AbusePattern {
  name: string
  description: string
  detection: {
    timeWindow: number // ms
    threshold: number
    conditions: Array<{
      field: keyof UsageMetrics
      operator: 'gt' | 'lt' | 'eq' | 'contains'
      value: any
    }>
  }
  action: 'warn' | 'throttle' | 'block'
  blockDuration?: number // ms
}

export interface AbuseDetectionResult {
  isAbuse: boolean
  patterns: string[]
  action: 'allow' | 'warn' | 'throttle' | 'block'
  blockUntil?: Date
  reason?: string
}

// Rate limit configurations for different scenarios
export const RATE_LIMIT_CONFIGS = {
  // Per-user limits
  USER_CHAT: {
    keyPrefix: 'user_chat',
    maxRequests: 60, // requests per minute
    windowMs: 60 * 1000,
    blockDurationMs: 60 * 1000,
  } as RateLimitConfig,

  USER_CHAT_BURST: {
    keyPrefix: 'user_chat_burst',
    maxRequests: 10, // requests per 10 seconds
    windowMs: 10 * 1000,
    blockDurationMs: 30 * 1000,
  } as RateLimitConfig,

  // Per-workspace limits
  WORKSPACE_CHAT: {
    keyPrefix: 'workspace_chat',
    maxRequests: 1000, // requests per hour
    windowMs: 60 * 60 * 1000,
    blockDurationMs: 5 * 60 * 1000,
  } as RateLimitConfig,

  // Per-IP limits (for unauthenticated requests)
  IP_GLOBAL: {
    keyPrefix: 'ip_global',
    maxRequests: 20, // requests per minute
    windowMs: 60 * 1000,
    blockDurationMs: 5 * 60 * 1000,
  } as RateLimitConfig,

  // Provider-specific limits
  PROVIDER_OPENAI: {
    keyPrefix: 'provider_openai',
    maxRequests: 500, // requests per hour per workspace
    windowMs: 60 * 60 * 1000,
    blockDurationMs: 10 * 60 * 1000,
  } as RateLimitConfig,

  PROVIDER_ANTHROPIC: {
    keyPrefix: 'provider_anthropic',
    maxRequests: 300, // requests per hour per workspace
    windowMs: 60 * 60 * 1000,
    blockDurationMs: 10 * 60 * 1000,
  } as RateLimitConfig,

  PROVIDER_GOOGLE: {
    keyPrefix: 'provider_google',
    maxRequests: 200, // requests per hour per workspace
    windowMs: 60 * 60 * 1000,
    blockDurationMs: 10 * 60 * 1000,
  } as RateLimitConfig,

  // Websocket connection limits
  WS_CONNECTIONS: {
    keyPrefix: 'ws_connections',
    maxRequests: 5, // concurrent connections per user
    windowMs: 60 * 1000,
    blockDurationMs: 2 * 60 * 1000,
  } as RateLimitConfig,

  // Cost-based throttling
  COST_DAILY: {
    keyPrefix: 'cost_daily',
    maxRequests: 1000, // $10.00 daily limit (in cents)
    windowMs: 24 * 60 * 60 * 1000,
    blockDurationMs: 60 * 60 * 1000,
  } as RateLimitConfig,

  COST_MONTHLY: {
    keyPrefix: 'cost_monthly',
    maxRequests: 10000, // $100.00 monthly limit (in cents)
    windowMs: 30 * 24 * 60 * 60 * 1000,
    blockDurationMs: 24 * 60 * 60 * 1000,
  } as RateLimitConfig,
} as const

// Cost configurations for different providers
export const COST_CONFIGS: Record<string, Record<string, CostConfig>> = {
  openai: {
    'gpt-4': {
      provider: 'openai',
      model: 'gpt-4',
      inputCostPer1k: 3.0, // $0.03 per 1k tokens
      outputCostPer1k: 6.0, // $0.06 per 1k tokens
      requestBaseCost: 0.1, // $0.001 per request
    },
    'gpt-4-turbo': {
      provider: 'openai',
      model: 'gpt-4-turbo',
      inputCostPer1k: 1.0,
      outputCostPer1k: 3.0,
      requestBaseCost: 0.1,
    },
    'gpt-3.5-turbo': {
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      inputCostPer1k: 0.15,
      outputCostPer1k: 0.2,
      requestBaseCost: 0.05,
    },
  },
  anthropic: {
    'claude-3-opus': {
      provider: 'anthropic',
      model: 'claude-3-opus',
      inputCostPer1k: 1.5,
      outputCostPer1k: 7.5,
      requestBaseCost: 0.1,
    },
    'claude-3-sonnet': {
      provider: 'anthropic',
      model: 'claude-3-sonnet',
      inputCostPer1k: 0.3,
      outputCostPer1k: 1.5,
      requestBaseCost: 0.05,
    },
    'claude-3-haiku': {
      provider: 'anthropic',
      model: 'claude-3-haiku',
      inputCostPer1k: 0.025,
      outputCostPer1k: 0.125,
      requestBaseCost: 0.01,
    },
  },
  google: {
    'gemini-pro': {
      provider: 'google',
      model: 'gemini-pro',
      inputCostPer1k: 0.05,
      outputCostPer1k: 0.15,
      requestBaseCost: 0.02,
    },
    'gemini-pro-vision': {
      provider: 'google',
      model: 'gemini-pro-vision',
      inputCostPer1k: 0.25,
      outputCostPer1k: 0.5,
      requestBaseCost: 0.05,
    },
    'gemini-1.5-pro': {
      provider: 'google',
      model: 'gemini-1.5-pro',
      inputCostPer1k: 0.125, // $0.00125 per 1k tokens
      outputCostPer1k: 0.375, // $0.00375 per 1k tokens
      requestBaseCost: 0.02,
    },
    'gemini-1.5-flash': {
      provider: 'google',
      model: 'gemini-1.5-flash',
      inputCostPer1k: 0.075, // $0.00075 per 1k tokens
      outputCostPer1k: 0.3, // $0.003 per 1k tokens
      requestBaseCost: 0.01,
    },
    'gemini-2.0-flash-exp': {
      provider: 'google',
      model: 'gemini-2.0-flash-exp',
      inputCostPer1k: 0.075,
      outputCostPer1k: 0.3,
      requestBaseCost: 0.01,
    },
  },
}

// Default abuse patterns
export const ABUSE_PATTERNS: AbusePattern[] = [
  {
    name: 'high_frequency_requests',
    description: 'Unusually high request frequency',
    detection: {
      timeWindow: 60 * 1000, // 1 minute
      threshold: 100,
      conditions: [
        { field: 'requestCount', operator: 'gt', value: 100 },
      ],
    },
    action: 'throttle',
    blockDuration: 5 * 60 * 1000, // 5 minutes
  },
  {
    name: 'excessive_token_usage',
    description: 'Extremely high token consumption',
    detection: {
      timeWindow: 60 * 1000,
      threshold: 1,
      conditions: [
        { field: 'totalTokens', operator: 'gt', value: 50000 },
      ],
    },
    action: 'warn',
  },
  {
    name: 'cost_spike',
    description: 'Sudden cost increase',
    detection: {
      timeWindow: 5 * 60 * 1000, // 5 minutes
      threshold: 1,
      conditions: [
        { field: 'cost', operator: 'gt', value: 500 }, // $5.00 in 5 minutes
      ],
    },
    action: 'block',
    blockDuration: 30 * 60 * 1000, // 30 minutes
  },
  {
    name: 'repeated_identical_requests',
    description: 'Multiple identical requests in short time',
    detection: {
      timeWindow: 30 * 1000, // 30 seconds
      threshold: 5,
      conditions: [
        { field: 'requestCount', operator: 'gt', value: 5 },
      ],
    },
    action: 'throttle',
    blockDuration: 2 * 60 * 1000, // 2 minutes
  },
]
