export interface AiMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  model?: string
  workspaceId?: string
  conversationId?: string
  artifacts?: AiArtifact[]
}

export interface AiConversation {
  id: string
  title: string
  workspaceId: string
  userId: string
  messages: AiMessage[]
  model: string
  provider: string
  integrationId: string
  createdAt: Date
  updatedAt: Date
  metadata?: Record<string, any>
}

export interface AiModel {
  id: string
  name: string
  provider: 'openai' | 'anthropic' | 'google' | 'mistral' | 'groq' | 'together' | 'huggingface' | 'ollama' | 'xai'
  model: string
  description?: string
  capabilities?: string[]
  contextWindow?: number
  maxTokens?: number
  integrationId?: string
}

export interface AiStreamMessage {
  type: 'init' | 'message' | 'stream' | 'error' | 'end'
  data?: any
  error?: string
}

export interface AiChatConfig {
  integrationId: string
  model: string
  provider: string
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  systemPrompt?: string
  enableMCP?: boolean
  mcpTools?: string[]
}

export interface AiWebSocketMessage {
  type: 'init' | 'chat' | 'ping' | 'pong' | 'error' | 'stream_start' | 'stream_chunk' | 'stream_end' | 'artifact_start' | 'artifact_chunk' | 'artifact_end'
  data?: {
    message?: string
    messages?: AiMessage[]
    config?: AiChatConfig
    conversationId?: string
    chunk?: string
    error?: string
    artifact?: AiArtifact
    artifactId?: string
    metrics?: AiUsageMetrics
  }
  timestamp?: number
}

export interface AiProviderConfig {
  provider: string
  apiKey?: string
  baseUrl?: string
  models: AiModel[]
}

export interface AiUsageMetrics {
  promptTokens: number
  completionTokens: number
  totalTokens: number
  cost?: number
}

export interface AiChatState {
  isConnected: boolean
  isStreaming: boolean
  currentMessage: string
  messages: AiMessage[]
  error: string | null
  conversationId: string | null
  metrics?: AiUsageMetrics
  currentArtifacts?: AiArtifact[]
  expandedArtifactId?: string
}

// Artifact types
export type AiArtifactType = 'weather' | 'document' | 'code' | 'chart' | 'image' | 'table'

export interface AiArtifact {
  id: string
  type: AiArtifactType
  title: string
  content: AiArtifactContent
  metadata?: Record<string, any>
}

export type AiArtifactContent
  = | AiWeatherContent
    | AiDocumentContent
    | AiCodeContent
    | AiChartContent
    | AiImageContent
    | AiTableContent

export interface AiWeatherContent {
  type: 'weather'
  location: string
  temperature: number
  condition: string
  humidity?: number
  windSpeed?: number
  forecast?: Array<{
    date: string
    high: number
    low: number
    condition: string
  }>
}

export interface AiDocumentContent {
  type: 'document'
  format: 'markdown' | 'text' | 'html'
  content: string
  editable?: boolean
}

export interface AiCodeContent {
  type: 'code'
  language: string
  code: string
  filename?: string
  executable?: boolean
}

export interface AiChartContent {
  type: 'chart'
  chartType: 'line' | 'bar' | 'pie' | 'scatter'
  data: any
  options?: any
}

export interface AiImageContent {
  type: 'image'
  url?: string
  base64?: string
  alt?: string
  width?: number
  height?: number
}

export interface AiTableContent {
  type: 'table'
  headers: string[]
  rows: any[][]
  sortable?: boolean
  searchable?: boolean
}
