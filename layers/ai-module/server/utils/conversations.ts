import type { AiConversation } from '../../types/ai'
import { getFirestoreAdmin } from '../../../auth-module/server/utils/firebase-admin'

export async function getConversation(
  conversationId: string,
  workspaceId: string,
  userId: string,
  _idToken?: string,
): Promise<AiConversation | null> {
  try {
    const firestore = getFirestoreAdmin()
    const conversationRef = firestore.collection('ai_conversations').doc(conversationId)
    const conversationSnap = await conversationRef.get()

    if (!conversationSnap.exists) {
      return null
    }

    const conversation = conversationSnap.data() as AiConversation

    // Verify the conversation belongs to the user and workspace
    if (conversation.workspaceId !== workspaceId || conversation.userId !== userId) {
      console.warn(`Access denied: conversation ${conversationId} doesn't belong to user ${userId} in workspace ${workspaceId}`)
      return null
    }

    return conversation
  }
  catch (error) {
    console.error('Error getting conversation:', error)
    return null
  }
}

export async function createConversation(data: {
  workspaceId: string
  userId: string
  title?: string
  metadata?: Record<string, any>
  idToken?: string
}): Promise<AiConversation> {
  try {
    console.log('[AI Conversations] Creating conversation with data:', {
      workspaceId: data.workspaceId,
      userId: data.userId,
      title: data.title,
      hasMetadata: !!data.metadata,
    })
    
    const firestore = getFirestoreAdmin()
    const conversationId = generateConversationId()

    const conversation: AiConversation = {
      id: conversationId,
      title: data.title || 'New Conversation',
      workspaceId: data.workspaceId,
      userId: data.userId,
      messages: [],
      model: data.metadata?.model || '',
      provider: data.metadata?.provider || '',
      integrationId: data.metadata?.integrationId || '',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: data.metadata || {},
    }

    // Create the document in Firestore using Admin SDK
    const conversationRef = firestore.collection('ai_conversations').doc(conversationId)
    const firestoreData = {
      ...conversation,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    await conversationRef.set(firestoreData)

    console.log('Created conversation:', conversationId)
    return conversation
  }
  catch (error) {
    console.error('[AI Conversations] Error creating conversation:', error)
    console.error('[AI Conversations] Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    })
    throw createError({
      statusCode: 500,
      statusMessage: `Failed to create conversation: ${error instanceof Error ? error.message : 'Unknown error'}`,
    })
  }
}

export async function updateConversation(
  conversationId: string,
  workspaceId: string,
  userId: string,
  updates: Partial<AiConversation>,
  _idToken?: string,
): Promise<AiConversation | null> {
  try {
    // First verify the conversation exists and belongs to the user
    const existing = await getConversation(conversationId, workspaceId, userId, _idToken)
    if (!existing) {
      return null
    }

    const firestore = getFirestoreAdmin()
    const conversationRef = firestore.collection('ai_conversations').doc(conversationId)

    const updateData = {
      ...updates,
      updatedAt: new Date(),
    }

    await conversationRef.update(updateData)

    console.log(`Updated conversation ${conversationId}`)
    return { ...existing, ...updates, updatedAt: new Date() } as AiConversation
  }
  catch (error) {
    console.error('Error updating conversation:', error)
    return null
  }
}

export async function deleteConversation(
  conversationId: string,
  workspaceId: string,
  userId: string,
  _idToken?: string,
): Promise<boolean> {
  try {
    // First verify the conversation exists and belongs to the user
    const existing = await getConversation(conversationId, workspaceId, userId, _idToken)
    if (!existing) {
      return false
    }

    const firestore = getFirestoreAdmin()
    const conversationRef = firestore.collection('ai_conversations').doc(conversationId)

    // Soft delete by setting deletedAt timestamp
    await conversationRef.update({
      deletedAt: new Date(),
      updatedAt: new Date(),
    })

    console.log(`Deleted conversation ${conversationId}`)
    return true
  }
  catch (error) {
    console.error('Error deleting conversation:', error)
    return false
  }
}

function generateConversationId(): string {
  return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
