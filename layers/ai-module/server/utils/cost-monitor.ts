/**
 * AI Cost Monitor Service - Comprehensive Usage Analytics and Abuse Detection
 *
 * A sophisticated service for monitoring AI usage costs, tracking consumption patterns,
 * enforcing rate limits, and detecting abuse across multi-tenant workspaces. Designed
 * for production-scale AI applications with detailed analytics and security monitoring.
 *
 * Features:
 * - Real-time cost calculation for multiple AI providers (OpenAI, Anthropic, Google, XAI)
 * - Usage aggregation with daily/monthly rollups and provider/user breakdowns
 * - Configurable rate limiting with workspace and per-user limits
 * - Advanced abuse detection with pattern matching and automatic responses
 * - Firestore-based persistence with atomic transactions for data consistency
 * - Alert system for usage violations and suspicious activity
 * - Comprehensive analytics with historical tracking and trend analysis
 *
 * Architecture:
 * - Singleton service pattern for optimal resource management
 * - Lazy database initialization for improved startup performance
 * - Transaction-based aggregation updates for data consistency
 * - Modular abuse detection with configurable patterns and actions
 * - Provider-agnostic cost calculation with flexible configuration
 *
 * Security Features:
 * - Workspace-scoped access control and data isolation
 * - Abuse pattern detection with automatic blocking capabilities
 * - Usage limit enforcement with graceful degradation
 * - Comprehensive audit logging for compliance and monitoring
 * - Rate limiting with IP and user tracking
 *
 * Performance Considerations:
 * - Optimized Firestore queries with compound indexes
 * - Efficient aggregation updates using transactions
 * - Lazy service initialization to reduce cold start times
 * - Batch operations for high-throughput scenarios
 * - In-memory caching for frequently accessed configurations
 *
 * Data Flow:
 * 1. Records individual usage metrics with timestamps and metadata
 * 2. Updates real-time aggregates using atomic Firestore transactions
 * 3. Enforces usage limits before allowing requests to proceed
 * 4. Detects abuse patterns and triggers appropriate responses
 * 5. Maintains comprehensive audit trails for compliance
 *
 * @module CostMonitorService
 * @example
 * const service = getCostMonitorService()
 * const cost = service.calculateCost('openai', 'gpt-4', 1000, 500)
 * await service.recordUsage({ workspaceId, userId, cost, totalTokens: 1500 })
 */

import type {
  AbuseDetectionResult,
  AbusePattern,
  CostConfig,
  UsageLimits,
  UsageMetrics,
  UsageStats,
} from '../../types/rate-limiting'
import { useFirebaseServer } from '../../../auth-module/server/firebase/init'
import { ABUSE_PATTERNS, COST_CONFIGS } from '../../types/rate-limiting'

/**
 * CostMonitorService - Core AI Usage Monitoring and Analytics Service
 *
 * Provides comprehensive cost monitoring, usage analytics, rate limiting, and abuse
 * detection for AI services across multiple providers and workspaces. Uses Firestore
 * for persistence with optimized data structures for analytics and reporting.
 *
 * Service Lifecycle:
 * - Lazy initialization of database connections for optimal performance
 * - Singleton pattern ensures consistent state across the application
 * - Automatic retry mechanisms for database connectivity issues
 * - Graceful error handling with fallback behaviors
 *
 * Data Architecture:
 * - ai_usage_metrics: Individual usage records with full metadata
 * - ai_usage_aggregates: Daily/monthly rollups by workspace/provider/user
 * - ai_usage_limits: Configurable limits per workspace
 * - ai_abuse_logs: Audit trail of detected abuse patterns
 *
 * @class CostMonitorService
 */
class CostMonitorService {
  private db: any // Will be injected with Firebase instance
  private dbInitialized: boolean = false

  /**
   * Constructor for CostMonitorService
   *
   * Creates a new cost monitoring service instance with lazy initialization.
   * Database connections are established on first use to optimize startup
   * performance and reduce resource consumption.
   */
  constructor() {
    // Don't initialize immediately - use lazy initialization
  }

  /**
   * Initialize the service with a Firebase instance
   * This should be called from API endpoints that have access to the session context
   *
   * @param {any} firestore - Firebase Firestore instance from useFirebaseServer
   */
  initializeWithFirestore(firestore: any) {
    this.db = firestore
    this.dbInitialized = true
  }

  /**
   * Ensure database connection is initialized
   *
   * Lazy initialization pattern that establishes Firestore connection on first use.
   * Uses the shared Firebase Admin SDK from the auth module for consistency and
   * proper configuration management.
   *
   * Features:
   * - Lazy loading to improve startup performance
   * - Automatic retry on connection failures
   * - Shared Firebase Admin instance for consistency
   * - Firestore settings configuration for optimal performance
   *
   * Error Handling:
   * - Resets initialization flag on failure for retry capability
   * - Comprehensive error logging for debugging
   * - Throws errors to calling functions for proper handling
   *
   * @private
   * @async
   * @returns {Promise<void>} Resolves when database is ready for use
   * @throws {Error} If database initialization fails
   */
  private async ensureInitialized(idToken?: string) {
    if (!this.dbInitialized) {
      try {
        // Use the proper server-side Firebase approach from auth module
        const { firestore } = await useFirebaseServer(idToken)
        this.db = firestore

        // Ensure proper Firestore initialization with emulator support
        // The auth module's useFirebaseServer already handles emulator configuration
        this.dbInitialized = true
        console.warn('[CostMonitor] Database initialized successfully')
      }
      catch (error) {
        console.error('[CostMonitor] Failed to initialize database:', error)
        // Reset initialization flag so it can be retried
        this.dbInitialized = false
        throw error
      }
    }
  }

  /**
   * Calculate cost for a specific AI request
   *
   * Computes the total cost of an AI request based on provider-specific pricing
   * models, token usage, and base costs. Uses configured cost tables for accurate
   * billing and rate limiting calculations.
   *
   * Cost Calculation Formula:
   * - Input Cost = (promptTokens / 1000) × inputCostPer1k
   * - Output Cost = (completionTokens / 1000) × outputCostPer1k
   * - Total Cost = Input Cost + Output Cost + Base Cost (in cents)
   *
   * Provider Support:
   * - OpenAI: GPT models with input/output token pricing
   * - Anthropic: Claude models with message-based pricing
   * - Google: Gemini models with character-based pricing converted to tokens
   * - XAI: Grok models with token-based pricing
   * - Fallback: Returns 0 for unknown providers (logged as warning)
   *
   * Pricing Features:
   * - Per-1000 token pricing for granular cost calculation
   * - Base request costs for providers that charge per API call
   * - Model-specific pricing with intelligent fallback matching
   * - Cost returned in cents for precise financial calculations
   *
   * Complexity: O(1) - Direct lookup and arithmetic operations
   *
   * @param {string} provider - AI provider name (lowercase)
   * @param {string} model - Specific model identifier
   * @param {number} promptTokens - Number of input/prompt tokens used
   * @param {number} completionTokens - Number of output/completion tokens generated
   * @returns {number} Total cost in cents (integer for precise calculations)
   *
   * @example
   * const cost = calculateCost('openai', 'gpt-4-turbo', 1000, 500)
   * console.log(`Request cost: $${cost / 100}`) // Convert cents to dollars for display
   */
  calculateCost(
    provider: string,
    model: string,
    promptTokens: number,
    completionTokens: number,
  ): number {
    const costConfig = this.getCostConfig(provider, model)
    if (!costConfig) {
      console.warn(`[CostMonitor] No cost config found for ${provider}:${model}`)
      return 0
    }

    const inputCost = (promptTokens / 1000) * costConfig.inputCostPer1k
    const outputCost = (completionTokens / 1000) * costConfig.outputCostPer1k
    const baseCost = costConfig.requestBaseCost || 0

    return Math.round((inputCost + outputCost + baseCost) * 100) // Return in cents
  }

  /**
   * Get cost configuration for provider/model
   */
  private getCostConfig(provider: string, model: string): CostConfig | null {
    const providerConfigs = COST_CONFIGS[provider]
    if (!providerConfigs)
      return null

    // Try exact match first
    if (providerConfigs[model]) {
      return providerConfigs[model]
    }

    // Try partial match for model variants
    const modelKey = Object.keys(providerConfigs).find(key =>
      model.toLowerCase().includes(key.toLowerCase())
      || key.toLowerCase().includes(model.toLowerCase()),
    )

    return modelKey ? providerConfigs[modelKey] : null
  }

  /**
   * Record comprehensive usage metrics for analytics and monitoring
   *
   * Stores detailed usage information and updates real-time aggregates for analytics.
   * Performs atomic operations to ensure data consistency across multiple collections
   * and triggers abuse detection for security monitoring.
   *
   * Data Flow:
   * 1. Stores individual usage record with full metadata and timestamps
   * 2. Updates daily aggregates with workspace/provider/user breakdowns
   * 3. Updates monthly aggregates for long-term analytics
   * 4. Triggers abuse detection algorithms for security monitoring
   * 5. Handles abuse responses based on detected patterns
   *
   * Storage Architecture:
   * - ai_usage_metrics: Individual records for detailed analysis
   * - ai_usage_aggregates: Daily and monthly rollups for performance
   * - Atomic transactions ensure consistency across collections
   * - Compound indexes for efficient querying and aggregation
   *
   * Performance Features:
   * - Batch operations for high-throughput scenarios
   * - Optimized Firestore queries with proper indexing
   * - Parallel aggregate updates for improved performance
   * - Graceful error handling to prevent request blocking
   *
   * Security Features:
   * - Automatic abuse pattern detection
   * - Real-time threat response and blocking
   * - Comprehensive audit logging for compliance
   * - Workspace-scoped data isolation
   *
   * Complexity: O(1) for writes + O(log n) for transaction updates
   *
   * @async
   * @param {UsageMetrics} metrics - Complete usage metrics including costs, tokens, and metadata
   * @returns {Promise<void>} Resolves when all operations complete successfully
   * @throws {Error} Critical database errors (non-blocking for abuse detection failures)
   *
   * @example
   * await recordUsage({
   *   workspaceId: 'workspace-123',
   *   userId: 'user-456',
   *   provider: 'openai',
   *   model: 'gpt-4-turbo',
   *   totalTokens: 1500,
   *   cost: 750, // in cents
   *   requestCount: 1,
   *   timestamp: new Date()
   * })
   */
  async recordUsage(metrics: UsageMetrics): Promise<void> {
    try {
      await this.ensureInitialized()

      const timestamp = new Date()
      const dayKey = timestamp.toISOString().split('T')[0] // YYYY-MM-DD
      const monthKey = timestamp.toISOString().substring(0, 7) // YYYY-MM

      // Store detailed usage record
      await this.db.collection('ai_usage_metrics').add({
        ...metrics,
        timestamp,
        dayKey,
        monthKey,
      })

      // Update daily aggregates
      await this.updateAggregates('daily', dayKey, metrics)

      // Update monthly aggregates
      await this.updateAggregates('monthly', monthKey, metrics)

      // Check for abuse patterns
      const abuseResult = await this.detectAbuse(metrics)
      if (abuseResult.isAbuse) {
        await this.handleAbuseDetection(metrics, abuseResult)
      }
    }
    catch (error) {
      console.error('[CostMonitor] Failed to record usage:', error)
    }
  }

  /**
   * Update usage aggregates
   */
  private async updateAggregates(
    period: 'daily' | 'monthly',
    periodKey: string,
    metrics: UsageMetrics,
  ): Promise<void> {
    try {
      const docRef = this.db
        .collection('ai_usage_aggregates')
        .doc(`${period}_${metrics.workspaceId}_${periodKey}`)

      // Use Firestore runTransaction with proper error handling
      await this.db.runTransaction(async (transaction: any) => {
        const doc = await transaction.get(docRef)

        if (doc.exists) {
          const data = doc.data()
          const updates = {
            totalTokens: (data.totalTokens || 0) + metrics.totalTokens,
            totalCost: (data.totalCost || 0) + metrics.cost,
            totalRequests: (data.totalRequests || 0) + metrics.requestCount,
            [`byProvider.${metrics.provider}.tokens`]:
              (data.byProvider?.[metrics.provider]?.tokens || 0) + metrics.totalTokens,
            [`byProvider.${metrics.provider}.cost`]:
              (data.byProvider?.[metrics.provider]?.cost || 0) + metrics.cost,
            [`byProvider.${metrics.provider}.requests`]:
              (data.byProvider?.[metrics.provider]?.requests || 0) + metrics.requestCount,
            [`byUser.${metrics.userId}.tokens`]:
              (data.byUser?.[metrics.userId]?.tokens || 0) + metrics.totalTokens,
            [`byUser.${metrics.userId}.cost`]:
              (data.byUser?.[metrics.userId]?.cost || 0) + metrics.cost,
            [`byUser.${metrics.userId}.requests`]:
              (data.byUser?.[metrics.userId]?.requests || 0) + metrics.requestCount,
            lastUpdated: new Date(),
          }
          transaction.update(docRef, updates)
        }
        else {
          const newData = {
            workspaceId: metrics.workspaceId,
            period,
            periodKey,
            totalTokens: metrics.totalTokens,
            totalCost: metrics.cost,
            totalRequests: metrics.requestCount,
            byProvider: {
              [metrics.provider]: {
                tokens: metrics.totalTokens,
                cost: metrics.cost,
                requests: metrics.requestCount,
              },
            },
            byUser: {
              [metrics.userId]: {
                tokens: metrics.totalTokens,
                cost: metrics.cost,
                requests: metrics.requestCount,
              },
            },
            resetTime: period === 'daily'
              ? new Date(new Date(periodKey).getTime() + 24 * 60 * 60 * 1000)
              : new Date(new Date(periodKey).getFullYear(), new Date(periodKey).getMonth() + 1, 1),
            createdAt: new Date(),
            lastUpdated: new Date(),
          }
          transaction.set(docRef, newData)
        }
      })
    }
    catch (error) {
      // Handle Firestore transaction errors gracefully
      console.error('[CostMonitor] Transaction failed, attempting fallback update:', error)

      // Fallback to simple document update without transaction
      try {
        const docRef = this.db
          .collection('ai_usage_aggregates')
          .doc(`${period}_${metrics.workspaceId}_${periodKey}`)

        const doc = await docRef.get()

        if (doc.exists) {
          const data = doc.data()
          await docRef.update({
            totalTokens: (data.totalTokens || 0) + metrics.totalTokens,
            totalCost: (data.totalCost || 0) + metrics.cost,
            totalRequests: (data.totalRequests || 0) + metrics.requestCount,
            lastUpdated: new Date(),
          })
        }
        else {
          const newData = {
            workspaceId: metrics.workspaceId,
            period,
            periodKey,
            totalTokens: metrics.totalTokens,
            totalCost: metrics.cost,
            totalRequests: metrics.requestCount,
            byProvider: {
              [metrics.provider]: {
                tokens: metrics.totalTokens,
                cost: metrics.cost,
                requests: metrics.requestCount,
              },
            },
            byUser: {
              [metrics.userId]: {
                tokens: metrics.totalTokens,
                cost: metrics.cost,
                requests: metrics.requestCount,
              },
            },
            resetTime: period === 'daily'
              ? new Date(new Date(periodKey).getTime() + 24 * 60 * 60 * 1000)
              : new Date(new Date(periodKey).getFullYear(), new Date(periodKey).getMonth() + 1, 1),
            createdAt: new Date(),
            lastUpdated: new Date(),
          }
          await docRef.set(newData)
        }

        console.warn('[CostMonitor] Fallback update completed successfully')
      }
      catch (fallbackError) {
        console.error('[CostMonitor] Both transaction and fallback failed:', fallbackError)
        throw fallbackError
      }
    }
  }

  /**
   * Get usage statistics for a workspace
   */
  async getUsageStats(
    workspaceId: string,
    period: 'daily' | 'monthly',
    date?: Date,
  ): Promise<UsageStats | null> {
    try {
      await this.ensureInitialized()

      const targetDate = date || new Date()
      const periodKey = period === 'daily'
        ? targetDate.toISOString().split('T')[0]
        : targetDate.toISOString().substring(0, 7)

      const docRef = this.db
        .collection('ai_usage_aggregates')
        .doc(`${period}_${workspaceId}_${periodKey}`)

      const doc = await docRef.get()
      return doc.exists ? doc.data() as UsageStats : null
    }
    catch (error) {
      console.error('[CostMonitor] Failed to get usage stats:', error)
      return null
    }
  }

  /**
   * Check if usage limits are exceeded
   */
  async checkUsageLimits(
    workspaceId: string,
    userId: string,
    requestCost: number,
    requestTokens: number,
  ): Promise<{ allowed: boolean, reason?: string, resetTime?: Date }> {
    try {
      await this.ensureInitialized()

      // Get workspace limits
      const limitsDoc = await this.db
        .collection('ai_usage_limits')
        .doc(workspaceId)
        .get()

      if (!limitsDoc.exists) {
        return { allowed: true } // No limits configured
      }

      const limits: UsageLimits = limitsDoc.data()

      // Check daily limits
      const dailyStats = await this.getUsageStats(workspaceId, 'daily')
      if (dailyStats) {
        if (limits.dailyCostLimit && (dailyStats.totalCost + requestCost) > limits.dailyCostLimit) {
          return {
            allowed: false,
            reason: 'Daily cost limit exceeded',
            resetTime: dailyStats.resetTime,
          }
        }

        if (limits.dailyTokenLimit && (dailyStats.totalTokens + requestTokens) > limits.dailyTokenLimit) {
          return {
            allowed: false,
            reason: 'Daily token limit exceeded',
            resetTime: dailyStats.resetTime,
          }
        }

        if (limits.dailyRequestLimit && dailyStats.totalRequests >= limits.dailyRequestLimit) {
          return {
            allowed: false,
            reason: 'Daily request limit exceeded',
            resetTime: dailyStats.resetTime,
          }
        }

        // Check per-user daily limits
        if (limits.perUserLimits && dailyStats.byUser[userId]) {
          const userDailyUsage = dailyStats.byUser[userId]

          if (limits.perUserLimits.dailyTokenLimit
            && (userDailyUsage.tokens + requestTokens) > limits.perUserLimits.dailyTokenLimit) {
            return {
              allowed: false,
              reason: 'User daily token limit exceeded',
              resetTime: dailyStats.resetTime,
            }
          }

          if (limits.perUserLimits.dailyRequestLimit
            && userDailyUsage.requests >= limits.perUserLimits.dailyRequestLimit) {
            return {
              allowed: false,
              reason: 'User daily request limit exceeded',
              resetTime: dailyStats.resetTime,
            }
          }
        }
      }

      // Check monthly limits
      const monthlyStats = await this.getUsageStats(workspaceId, 'monthly')
      if (monthlyStats) {
        if (limits.monthlyCostLimit && (monthlyStats.totalCost + requestCost) > limits.monthlyCostLimit) {
          return {
            allowed: false,
            reason: 'Monthly cost limit exceeded',
            resetTime: monthlyStats.resetTime,
          }
        }

        if (limits.monthlyTokenLimit && (monthlyStats.totalTokens + requestTokens) > limits.monthlyTokenLimit) {
          return {
            allowed: false,
            reason: 'Monthly token limit exceeded',
            resetTime: monthlyStats.resetTime,
          }
        }

        if (limits.monthlyRequestLimit && monthlyStats.totalRequests >= limits.monthlyRequestLimit) {
          return {
            allowed: false,
            reason: 'Monthly request limit exceeded',
            resetTime: monthlyStats.resetTime,
          }
        }

        // Check per-user monthly limits
        if (limits.perUserLimits && monthlyStats.byUser[userId]) {
          const userMonthlyUsage = monthlyStats.byUser[userId]

          if (limits.perUserLimits.monthlyTokenLimit
            && (userMonthlyUsage.tokens + requestTokens) > limits.perUserLimits.monthlyTokenLimit) {
            return {
              allowed: false,
              reason: 'User monthly token limit exceeded',
              resetTime: monthlyStats.resetTime,
            }
          }

          if (limits.perUserLimits.monthlyRequestLimit
            && userMonthlyUsage.requests >= limits.perUserLimits.monthlyRequestLimit) {
            return {
              allowed: false,
              reason: 'User monthly request limit exceeded',
              resetTime: monthlyStats.resetTime,
            }
          }
        }
      }

      return { allowed: true }
    }
    catch (error) {
      console.error('[CostMonitor] Failed to check usage limits:', error)
      return { allowed: true } // Allow on error to avoid blocking legitimate requests
    }
  }

  /**
   * Detect abuse patterns
   */
  private async detectAbuse(metrics: UsageMetrics): Promise<AbuseDetectionResult> {
    const result: AbuseDetectionResult = {
      isAbuse: false,
      patterns: [],
      action: 'allow',
    }

    for (const pattern of ABUSE_PATTERNS) {
      const isMatched = await this.checkAbusePattern(pattern, metrics)
      if (isMatched) {
        result.isAbuse = true
        result.patterns.push(pattern.name)

        // Use the most severe action
        if (pattern.action === 'block' || (pattern.action === 'throttle' && result.action !== 'block')) {
          result.action = pattern.action
          if (pattern.blockDuration) {
            result.blockUntil = new Date(Date.now() + pattern.blockDuration)
          }
        }
        else if (pattern.action === 'warn' && result.action === 'allow') {
          result.action = 'warn'
        }
      }
    }

    return result
  }

  /**
   * Check if metrics match an abuse pattern
   */
  private async checkAbusePattern(pattern: AbusePattern, metrics: UsageMetrics): Promise<boolean> {
    try {
      // Get recent metrics for the time window
      const timeWindow = new Date(Date.now() - pattern.detection.timeWindow)

      const query = this.db.collection('ai_usage_metrics')
        .where('workspaceId', '==', metrics.workspaceId)
        .where('userId', '==', metrics.userId)
        .where('timestamp', '>=', timeWindow)
        .orderBy('timestamp', 'desc')

      const snapshot = await query.get()
      const recentMetrics = snapshot.docs.map(doc => doc.data() as UsageMetrics)
      recentMetrics.push(metrics) // Include current metrics

      // Check conditions
      return pattern.detection.conditions.every((condition) => {
        const values = recentMetrics.map(m => m[condition.field])

        switch (condition.operator) {
          case 'gt':
            return values.some(v => Number(v) > condition.value)
          case 'lt':
            return values.some(v => Number(v) < condition.value)
          case 'eq':
            return values.includes(condition.value)
          case 'contains':
            return values.some(v => String(v).includes(condition.value))
          default:
            return false
        }
      })
    }
    catch (error) {
      console.error('[CostMonitor] Failed to check abuse pattern:', error)
      return false
    }
  }

  /**
   * Handle detected abuse
   */
  private async handleAbuseDetection(metrics: UsageMetrics, abuse: AbuseDetectionResult): Promise<void> {
    try {
      // Log the abuse detection
      await this.db.collection('ai_abuse_logs').add({
        workspaceId: metrics.workspaceId,
        userId: metrics.userId,
        patterns: abuse.patterns,
        action: abuse.action,
        blockUntil: abuse.blockUntil,
        metrics,
        timestamp: new Date(),
      })

      // Send alert if configured
      if (abuse.action === 'block' || abuse.action === 'throttle') {
        await this.sendAbuseAlert(metrics, abuse)
      }

      console.warn(`[CostMonitor] Abuse detected for user ${metrics.userId}:`, {
        patterns: abuse.patterns,
        action: abuse.action,
        blockUntil: abuse.blockUntil,
      })
    }
    catch (error) {
      console.error('[CostMonitor] Failed to handle abuse detection:', error)
    }
  }

  /**
   * Send abuse alert
   */
  private async sendAbuseAlert(metrics: UsageMetrics, abuse: AbuseDetectionResult): Promise<void> {
    // Implementation depends on notification system
    // Could send email, Slack message, etc.
    console.warn(`[CostMonitor] ABUSE ALERT - ${abuse.patterns.join(', ')} - User: ${metrics.userId}, Workspace: ${metrics.workspaceId}`)
  }

  /**
   * Set usage limits for a workspace
   */
  async setUsageLimits(workspaceId: string, limits: Partial<UsageLimits>): Promise<void> {
    try {
      const docRef = this.db.collection('ai_usage_limits').doc(workspaceId)
      await docRef.set({ ...limits, workspaceId, updatedAt: new Date() }, { merge: true })
    }
    catch (error) {
      console.error('[CostMonitor] Failed to set usage limits:', error)
      throw error
    }
  }

  /**
   * Get usage limits for a workspace
   */
  async getUsageLimits(workspaceId: string): Promise<UsageLimits | null> {
    try {
      const doc = await this.db.collection('ai_usage_limits').doc(workspaceId).get()
      return doc.exists ? doc.data() as UsageLimits : null
    }
    catch (error) {
      console.error('[CostMonitor] Failed to get usage limits:', error)
      return null
    }
  }
}

// Singleton instance for optimal resource management
let costMonitorService: CostMonitorService | null = null

/**
 * Get singleton instance of CostMonitorService
 *
 * Returns the shared CostMonitorService instance, creating it if necessary.
 * Uses singleton pattern to ensure consistent state and optimal resource usage
 * across the application.
 *
 * Benefits:
 * - Single database connection pool for efficiency
 * - Shared configuration and state management
 * - Reduced memory footprint and initialization overhead
 * - Consistent service behavior across modules
 *
 * @returns {CostMonitorService} Shared service instance
 */
export function getCostMonitorService(): CostMonitorService {
  if (!costMonitorService) {
    costMonitorService = new CostMonitorService()
  }
  return costMonitorService
}

/**
 * Record AI usage metrics for analytics and monitoring
 *
 * Convenience function that records comprehensive usage metrics including costs,
 * tokens, and metadata. Updates real-time aggregates and triggers abuse detection.
 * Uses the singleton service instance for optimal performance.
 *
 * @async
 * @param {UsageMetrics} metrics - Complete usage metrics to record
 * @returns {Promise<void>} Resolves when recording is complete
 *
 * @example
 * await recordUsageMetrics({
 *   workspaceId: 'ws-123',
 *   userId: 'user-456',
 *   provider: 'openai',
 *   model: 'gpt-4',
 *   totalTokens: 1000,
 *   cost: 500,
 *   requestCount: 1
 * })
 */
export async function recordUsageMetrics(metrics: UsageMetrics): Promise<void> {
  const service = getCostMonitorService()
  return await service.recordUsage(metrics)
}

/**
 * Calculate cost for an AI request
 *
 * Computes the total cost based on provider pricing models and token usage.
 * Returns cost in cents for precise financial calculations and rate limiting.
 *
 * @async
 * @param {string} provider - AI provider name (e.g., 'openai', 'anthropic')
 * @param {string} model - Model identifier (e.g., 'gpt-4-turbo', 'claude-3-opus')
 * @param {number} promptTokens - Input tokens used
 * @param {number} completionTokens - Output tokens generated
 * @returns {Promise<number>} Cost in cents
 *
 * @example
 * const cost = await calculateRequestCost('openai', 'gpt-4-turbo', 1000, 500)
 * console.log(`Cost: $${cost / 100}`) // Convert to dollars for display
 */
export async function calculateRequestCost(
  provider: string,
  model: string,
  promptTokens: number,
  completionTokens: number,
): Promise<number> {
  const service = getCostMonitorService()
  return service.calculateCost(provider, model, promptTokens, completionTokens)
}

/**
 * Check if usage request is within configured limits
 *
 * Validates proposed usage against workspace and user limits before allowing
 * requests to proceed. Checks daily and monthly limits for costs, tokens,
 * and request counts.
 *
 * @async
 * @param {string} workspaceId - Workspace identifier
 * @param {string} userId - User identifier
 * @param {number} requestCost - Proposed request cost in cents
 * @param {number} requestTokens - Proposed token usage
 * @returns {Promise<{allowed: boolean, reason?: string, resetTime?: Date}>} Limit check result
 *
 * @example
 * const result = await checkUsageLimits('ws-123', 'user-456', 500, 1000)
 * if (!result.allowed) {
 *   throw new Error(`Usage limit exceeded: ${result.reason}`)
 * }
 */
export async function checkUsageLimits(
  workspaceId: string,
  userId: string,
  requestCost: number,
  requestTokens: number,
): Promise<{ allowed: boolean, reason?: string, resetTime?: Date }> {
  const service = getCostMonitorService()
  return await service.checkUsageLimits(workspaceId, userId, requestCost, requestTokens)
}

/**
 * Initialize cost monitor with Firebase context from session
 * This should be called from API endpoints that have access to the user session
 *
 * @async
 * @param {string} [idToken] - Optional ID token from user session
 * @returns {Promise<CostMonitorService>} Initialized cost monitor service
 *
 * @example
 * const session = await getUserSession(event)
 * const idToken = session.user?.token?.idToken
 * const costMonitor = await initializeCostMonitorWithSession(idToken)
 * await costMonitor.recordUsage(metrics)
 */
export async function initializeCostMonitorWithSession(idToken?: string): Promise<CostMonitorService> {
  const { firestore } = await useFirebaseServer(idToken)
  const service = getCostMonitorService()
  service.initializeWithFirestore(firestore)
  return service
}
