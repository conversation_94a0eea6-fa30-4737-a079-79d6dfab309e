import type { H3Event } from 'h3'
import { getFirestoreAdmin } from '../../../auth-module/server/utils/firebase-admin'
import { decrypt } from '../../../auth-module/utils/encryption'

// Get encryption key from environment variable
function getEncryptionKey() {
  const key = process.env.NUXT_ENCRYPTION_KEY || process.env.ENCRYPTION_KEY
  if (!key) {
    throw new Error('Encryption key not configured')
  }
  return key
}

export async function getIntegrationWithApiKey(integrationId: string, workspaceId: string, _event?: H3Event) {
  console.warn('Integration lookup:', integrationId, workspaceId)

  try {
    // Use Firebase Admin SDK directly (no VertexAI initialization needed)
    const firestore = getFirestoreAdmin()

    console.warn(`[Integration] Fetching integration ${integrationId} for workspace ${workspaceId}`)
    // Use Admin SDK syntax
    const docRef = firestore.collection('integrations').doc(integrationId)
    const integrationDoc = await docRef.get()

    if (!integrationDoc.exists) {
      console.error(`Integration ${integrationId} not found`)
      return null
    }

    const integration = integrationDoc.data()
    console.warn(`[Integration] Found integration:`, integration)

    // Note: integrations are user-scoped, not workspace-scoped in this schema
    // So we skip workspace ownership check for now

    // Check if integration is active
    if (integration?.isActive !== true) {
      console.error(`Integration ${integrationId} is not active`)
      return null
    }

    // Get API key - it might be encrypted or stored directly
    let apiKey: string | undefined
    if (integration?.credentials?.apiKey) {
      // First try to use the API key directly (if it's already decrypted)
      apiKey = integration.credentials.apiKey

      // If it looks encrypted (base64 string), try to decrypt it
      if (apiKey && typeof apiKey === 'string' && apiKey.length > 50) {
        try {
          const encryptionKey = getEncryptionKey()
          apiKey = decrypt(apiKey, encryptionKey)
          console.warn(`[Integration] Successfully decrypted API key`)
        }
        catch (error) {
          console.warn(`[Integration] API key doesn't need decryption or decryption failed:`, error)
          // Keep the original key - it might not be encrypted
        }
      }
    }

    if (!apiKey) {
      console.error(`Integration ${integrationId} has no API key`)
      return null
    }

    return {
      id: integrationDoc.id,
      ...integration,
      credentials: {
        ...integration.credentials,
        apiKey,
      },
    }
  }
  catch (error) {
    console.error('Error fetching integration:', error)
    return null
  }
}
