import type { AiArtifact, AiChatConfig, AiMessage, AiWebSocketMessage } from '../../../types/ai'
import { anthropic } from '@ai-sdk/anthropic'
import { google } from '@ai-sdk/google'
import { openai } from '@ai-sdk/openai'
import { streamText, tool } from 'ai'
import { z } from 'zod'
import { sanitizeArtifactContent, securityLogger } from '../../../utils/security'
import { calculateRequestCost } from '../../utils/cost-monitor'
import { sanitizeConfig, sanitizeIds, sanitizeMessages } from '../../utils/input-sanitizer'
import { getIntegrationWithApiKey } from '../../utils/integrations'
import { checkApiLimits, recordApiUsage } from '../../utils/rate-limit-middleware'
import { validationSchemas } from '../../utils/validation'

interface WebSocketPeer {
  id: string
  send: (data: string) => void
  close: () => void
  headers?: Record<string, string>
}

interface PeerContext {
  config?: AiChatConfig
  workspaceId?: string
  userId?: string
  conversationId?: string
  ip?: string
  userAgent?: string
  connectionTime: number
  messageCount: number
  lastActivity: number
}

// Store peer contexts with rate limiting data
const peerContexts = new Map<string, PeerContext>()

// Connection rate limiting
const connectionAttempts = new Map<string, { count: number, resetTime: number }>()

export default defineWebSocketHandler({
  open(peer: WebSocketPeer) {
    console.log(`[WS] Client connected: ${peer.id}`)

    // Get client IP from headers
    const clientIp = peer.headers?.['x-forwarded-for']
      || peer.headers?.['x-real-ip']
      || '127.0.0.1'

    // Check connection rate limits
    const now = Date.now()
    const hour = 60 * 60 * 1000
    const connectionKey = `conn:${clientIp}`

    const attempts = connectionAttempts.get(connectionKey)
    if (attempts && now < attempts.resetTime) {
      if (attempts.count >= 10) { // Max 10 connections per hour per IP
        console.warn(`[WS] Connection rate limit exceeded for IP: ${clientIp}`)
        peer.send(JSON.stringify({
          type: 'error',
          data: { error: 'Connection rate limit exceeded. Try again later.' },
        }))
        peer.close()
        return
      }
      attempts.count++
    }
    else {
      connectionAttempts.set(connectionKey, { count: 1, resetTime: now + hour })
    }

    // Initialize peer context
    peerContexts.set(peer.id, {
      ip: clientIp,
      userAgent: peer.headers?.['user-agent'] || 'unknown',
      connectionTime: now,
      messageCount: 0,
      lastActivity: now,
    })

    // Clean up old connection attempts every hour
    if (Math.random() < 0.01) { // 1% chance on each connection
      for (const [key, data] of connectionAttempts.entries()) {
        if (now > data.resetTime) {
          connectionAttempts.delete(key)
        }
      }
    }
  },

  async message(peer: WebSocketPeer, message: string) {
    const context = peerContexts.get(peer.id)
    if (!context) {
      peer.send(JSON.stringify({
        type: 'error',
        data: { error: 'Context not found' },
      }))
      return
    }

    const now = Date.now()
    context.lastActivity = now
    context.messageCount++

    // Basic message rate limiting - max 60 messages per minute per connection
    const messageWindow = 60 * 1000 // 1 minute
    if (context.messageCount > 60 && (now - context.connectionTime) < messageWindow) {
      peer.send(JSON.stringify({
        type: 'error',
        data: { error: 'Message rate limit exceeded' },
      }))
      return
    }

    // Reset message count every minute
    if ((now - context.connectionTime) > messageWindow) {
      context.messageCount = 1
      context.connectionTime = now
    }

    try {
      const parsedMessage: AiWebSocketMessage = JSON.parse(message.toString())

      // Validate message size (1MB limit)
      if (message.length > 1024 * 1024) {
        peer.send(JSON.stringify({
          type: 'error',
          data: { error: 'Message too large' },
        }))
        return
      }

      switch (parsedMessage.type) {
        case 'init':
          await handleInit(peer, parsedMessage, context)
          break

        case 'chat':
          await handleChat(peer, parsedMessage, context)
          break

        case 'ping':
          peer.send(JSON.stringify({ type: 'pong' }))
          break

        default:
          peer.send(JSON.stringify({
            type: 'error',
            data: { error: 'Unknown message type' },
          }))
      }
    }
    catch (error) {
      console.error('[WS] Error handling message:', error)
      peer.send(JSON.stringify({
        type: 'error',
        data: { error: 'Failed to process message' },
      }))
    }
  },

  close(peer: WebSocketPeer) {
    console.log(`[WS] Client disconnected: ${peer.id}`)
    const context = peerContexts.get(peer.id)
    if (context) {
      const sessionDuration = Date.now() - context.connectionTime
      console.log(`[WS] Session stats for ${peer.id}:`, {
        duration: sessionDuration,
        messageCount: context.messageCount,
        userId: context.userId,
        workspaceId: context.workspaceId,
      })
    }
    peerContexts.delete(peer.id)
  },

  error(peer: WebSocketPeer, error: Error) {
    console.error(`[WS] Error for peer ${peer.id}:`, error)
    peer.send(JSON.stringify({
      type: 'error',
      data: { error: error.message },
    }))
  },
})

async function handleInit(peer: WebSocketPeer, message: AiWebSocketMessage, context: PeerContext) {
  const { config, workspaceId, userId } = message.data || {}

  if (!config || !workspaceId || !userId) {
    peer.send(JSON.stringify({
      type: 'error',
      data: { error: 'Missing required initialization data' },
    }))
    return
  }

  // Sanitize IDs
  const idSanitization = sanitizeIds(workspaceId, userId)
  if (!idSanitization.isValid) {
    peer.send(JSON.stringify({
      type: 'error',
      data: { error: `Invalid IDs: ${idSanitization.errors.join(', ')}` },
    }))
    return
  }

  // Sanitize config
  const configSanitization = sanitizeConfig(config)
  if (!configSanitization.isValid) {
    peer.send(JSON.stringify({
      type: 'error',
      data: { error: `Invalid config: ${configSanitization.errors.join(', ')}` },
    }))
    return
  }

  // Update peer context
  context.config = configSanitization.sanitizedData
  context.workspaceId = workspaceId
  context.userId = userId
  peerContexts.set(peer.id, context)

  // Validate integration
  try {
    const integration = await getIntegrationWithApiKey(config.integrationId, workspaceId)
    if (!integration) {
      peer.send(JSON.stringify({
        type: 'error',
        data: { error: 'Invalid integration' },
      }))
      return
    }
  }
  catch (error) {
    peer.send(JSON.stringify({
      type: 'error',
      data: { error: 'Failed to validate integration' },
    }))
    return
  }

  // Send init confirmation
  peer.send(JSON.stringify({
    type: 'init',
    data: {
      conversationId: message.data?.conversationId,
      rateLimits: {
        messagesPerMinute: 60,
        tokensPerHour: 100000,
        maxMessageSize: 50000,
      },
    },
  }))
}

async function handleChat(peer: WebSocketPeer, message: AiWebSocketMessage, context: PeerContext) {
  const startTime = Date.now()
  const { messages, conversationId } = message.data || {}

  if (!messages || !Array.isArray(messages)) {
    peer.send(JSON.stringify({
      type: 'error',
      data: { error: 'Messages array is required' },
    }))
    return
  }

  if (!context.config || !context.workspaceId || !context.userId) {
    peer.send(JSON.stringify({
      type: 'error',
      data: { error: 'Not initialized. Send init message first.' },
    }))
    return
  }

  // Update conversation ID if provided
  if (conversationId) {
    context.conversationId = conversationId
  }

  try {
    // Sanitize messages
    const messageSanitization = sanitizeMessages(messages)
    if (!messageSanitization.isValid) {
      peer.send(JSON.stringify({
        type: 'error',
        data: { error: `Invalid messages: ${messageSanitization.errors.join(', ')}` },
      }))
      return
    }

    const sanitizedMessages = messageSanitization.sanitizedData

    // Estimate token usage for rate limiting
    const estimatedTokens = estimateTokens(sanitizedMessages, context.config.systemPrompt)
    const estimatedCost = await calculateRequestCost(
      context.config.provider,
      context.config.model,
      Math.floor(estimatedTokens * 0.7), // Estimated prompt tokens
      Math.floor(estimatedTokens * 0.3), // Estimated completion tokens
    )

    // Rate limiting and cost checks
    const rateLimitContext = {
      userId: context.userId,
      workspaceId: context.workspaceId,
      ip: context.ip!,
      endpoint: '/api/ai/ws',
      provider: context.config.provider,
      model: context.config.model,
      userAgent: context.userAgent!,
      isAuthenticated: true,
    }

    const limitResult = await checkApiLimits(rateLimitContext, estimatedTokens, estimatedCost)

    if (!limitResult.allowed) {
      const errorMessage = limitResult.error
        || limitResult.costLimitResult?.reason
        || 'Request blocked by rate limiting'

      peer.send(JSON.stringify({
        type: 'error',
        data: {
          error: errorMessage,
          rateLimitInfo: limitResult.rateLimitResult,
          costLimitInfo: limitResult.costLimitResult,
        },
      }))
      return
    }

    // Get integration and decrypt API key
    const integration = await getIntegrationWithApiKey(context.config.integrationId, context.workspaceId)
    if (!integration || !integration.credentials?.apiKey) {
      peer.send(JSON.stringify({
        type: 'error',
        data: { error: 'Invalid integration or missing API key' },
      }))
      return
    }

    // Get the appropriate model based on provider
    const model = getModel(context.config.provider, context.config.model, integration.credentials.apiKey)
    if (!model) {
      peer.send(JSON.stringify({
        type: 'error',
        data: { error: 'Unsupported provider or model' },
      }))
      return
    }

    // Send stream start with rate limit info
    peer.send(JSON.stringify({
      type: 'stream_start',
      data: {
        rateLimitHeaders: limitResult.headers,
      },
    }))

    // Convert sanitized messages to AI SDK format
    const aiMessages = sanitizedMessages.map((msg: AiMessage) => ({
      role: msg.role as 'user' | 'assistant' | 'system',
      content: msg.content,
    }))

    // Add system prompt if configured
    if (context.config.systemPrompt) {
      aiMessages.unshift({
        role: 'system',
        content: context.config.systemPrompt,
      })
    }

    // Define tools for structured outputs
    const tools = {
      weather: tool({
        description: 'Get weather information for a location',
        parameters: z.object({
          location: z.string().describe('The location to get weather for'),
          temperature: z.number().describe('Current temperature in Celsius'),
          condition: z.string().describe('Weather condition (e.g., sunny, cloudy, rainy)'),
          humidity: z.number().optional().describe('Humidity percentage'),
          windSpeed: z.number().optional().describe('Wind speed in km/h'),
          forecast: z.array(z.object({
            date: z.string(),
            high: z.number(),
            low: z.number(),
            condition: z.string(),
          })).optional().describe('3-day forecast'),
        }),
        execute: async (args) => {
          // Validate and sanitize tool parameters
          const validatedArgs = validationSchemas.toolParameters.weather.parse(args)

          const artifactId = `weather-${Date.now()}`
          const artifact: AiArtifact = {
            id: artifactId,
            type: 'weather',
            title: `Weather in ${validatedArgs.location}`,
            content: {
              type: 'weather',
              ...validatedArgs,
            },
          }

          // Sanitize artifact content
          const sanitizationResult = sanitizeArtifactContent(artifact.content, 'weather')
          if (sanitizationResult.wasModified) {
            securityLogger.log({
              type: 'input_sanitized',
              severity: 'low',
              source: 'ws_weather_tool',
              details: { issues: sanitizationResult.issues, peerId: peer.id },
            })
          }

          artifact.content = sanitizationResult.sanitized || artifact.content

          // Send artifact to client
          peer.send(JSON.stringify({
            type: 'artifact_start',
            data: { artifact },
          }))

          return `Weather information for ${validatedArgs.location} has been displayed.`
        },
      }),

      document: tool({
        description: 'Create or display a document',
        parameters: z.object({
          title: z.string().describe('Document title'),
          content: z.string().describe('Document content'),
          format: z.enum(['markdown', 'text', 'html']).default('markdown'),
          editable: z.boolean().optional().default(true),
        }),
        execute: async (args) => {
          // Validate and sanitize tool parameters
          const validatedArgs = validationSchemas.toolParameters.document.parse(args)

          const artifactId = `doc-${Date.now()}`
          const artifact: AiArtifact = {
            id: artifactId,
            type: 'document',
            title: validatedArgs.title,
            content: {
              type: 'document',
              format: validatedArgs.format,
              content: validatedArgs.content,
              editable: validatedArgs.editable,
            },
          }

          // Sanitize artifact content
          const sanitizationResult = sanitizeArtifactContent(artifact.content, 'document')
          if (sanitizationResult.wasModified) {
            securityLogger.log({
              type: 'input_sanitized',
              severity: 'medium',
              source: 'ws_document_tool',
              details: { issues: sanitizationResult.issues, peerId: peer.id },
            })
          }

          artifact.content = sanitizationResult.sanitized || artifact.content

          // Send artifact start
          peer.send(JSON.stringify({
            type: 'artifact_start',
            data: { artifact },
          }))

          return `Document "${validatedArgs.title}" has been created.`
        },
      }),

      code: tool({
        description: 'Display code with syntax highlighting',
        parameters: z.object({
          title: z.string().describe('Title or description of the code'),
          language: z.string().describe('Programming language'),
          code: z.string().describe('The code to display'),
          filename: z.string().optional().describe('Optional filename'),
        }),
        execute: async (args) => {
          // Validate and sanitize tool parameters
          const validatedArgs = validationSchemas.toolParameters.code.parse(args)

          const artifactId = `code-${Date.now()}`
          const artifact: AiArtifact = {
            id: artifactId,
            type: 'code',
            title: validatedArgs.title,
            content: {
              type: 'code',
              language: validatedArgs.language,
              code: validatedArgs.code,
              filename: validatedArgs.filename,
            },
          }

          // Sanitize artifact content
          const sanitizationResult = sanitizeArtifactContent(artifact.content, 'code')
          if (sanitizationResult.wasModified) {
            securityLogger.log({
              type: 'input_sanitized',
              severity: 'medium',
              source: 'ws_code_tool',
              details: { issues: sanitizationResult.issues, peerId: peer.id },
            })
          }

          artifact.content = sanitizationResult.sanitized || artifact.content

          // Send artifact
          peer.send(JSON.stringify({
            type: 'artifact_start',
            data: { artifact },
          }))

          return `Code snippet "${validatedArgs.title}" has been displayed.`
        },
      }),
    }

    // Stream the response with tools
    const result = await streamText({
      model,
      messages: aiMessages,
      temperature: context.config.temperature || 0.7,
      maxTokens: context.config.maxTokens,
      topP: context.config.topP,
      frequencyPenalty: context.config.frequencyPenalty,
      presencePenalty: context.config.presencePenalty,
      tools,
      toolChoice: 'auto',
    })

    // Stream chunks to client
    for await (const chunk of result.textStream) {
      peer.send(JSON.stringify({
        type: 'stream_chunk',
        data: { chunk },
      }))
    }

    // Get usage metrics
    const usage = await result.usage

    // Record usage metrics
    if (usage) {
      const actualCost = await calculateRequestCost(
        context.config.provider,
        context.config.model,
        usage.promptTokens,
        usage.completionTokens,
      )

      await recordApiUsage(rateLimitContext, {
        promptTokens: usage.promptTokens,
        completionTokens: usage.completionTokens,
        cost: actualCost,
        provider: context.config.provider,
        model: context.config.model,
      })

      console.log('[WS] Usage recorded for peer:', peer.id, {
        promptTokens: usage.promptTokens,
        completionTokens: usage.completionTokens,
        totalTokens: usage.totalTokens,
        cost: actualCost,
        duration: Date.now() - startTime,
      })
    }

    // Send stream end with metrics
    peer.send(JSON.stringify({
      type: 'stream_end',
      data: {
        metrics: usage
          ? {
              promptTokens: usage.promptTokens,
              completionTokens: usage.completionTokens,
              totalTokens: usage.totalTokens,
            }
          : undefined,
        duration: Date.now() - startTime,
        rateLimitHeaders: limitResult.headers,
      },
    }))
  }
  catch (error) {
    console.error('[WS] Error in chat handler:', error)
    peer.send(JSON.stringify({
      type: 'error',
      data: { error: error instanceof Error ? error.message : 'Failed to generate response' },
    }))
  }
}

function getModel(provider: string, modelName: string, apiKey: string) {
  switch (provider) {
    case 'openai':
      return openai(modelName, { apiKey })

    case 'anthropic':
      return anthropic(modelName, { apiKey })

    case 'google':
      return google(modelName, { apiKey })

    default:
      return null
  }
}

/**
 * Estimate token count for a conversation
 */
function estimateTokens(messages: any[], systemPrompt?: string): number {
  let totalLength = 0

  // Add system prompt length
  if (systemPrompt) {
    totalLength += systemPrompt.length
  }

  // Add message lengths
  for (const message of messages) {
    totalLength += (message.content || '').length
  }

  // Rough estimate: 1 token per 4 characters for English text
  // Add 20% buffer for formatting and special tokens
  return Math.ceil((totalLength / 4) * 1.2)
}
