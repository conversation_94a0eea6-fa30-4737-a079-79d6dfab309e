import { getUserSession } from '../../../../../auth-module/server/utils/session'
import { createConversation } from '../../../utils/conversations'

export default defineEventHandler(async (event) => {
  try {
    // Get user session for authentication
    const session = await getUserSession(event)
    if (!session) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
      })
    }

    const { title, workspaceId, userId, metadata } = await readBody(event)

    // Use session data if not provided in body
    const finalWorkspaceId = workspaceId || session.currentWorkspace?.id
    const finalUserId = userId || session.user?.id

    if (!finalWorkspaceId || !finalUserId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required parameters',
      })
    }

    // Get the ID token for Firebase authentication
    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken

    const conversation = await createConversation({
      title: title || 'New Conversation',
      workspaceId: finalWorkspaceId,
      userId: finalUserId,
      metadata,
      idToken,
    })

    return conversation
  }
  catch (error: any) {
    console.error('[AI Conversations] Error creating conversation:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Failed to create conversation',
    })
  }
})
