import { getConnectionRecovery } from '../../utils/connection-recovery'
import { getSessionManager } from '../../utils/session-manager'
import { getWebSocketMonitor } from '../../utils/websocket-monitor'

export default defineEventHandler(async (event) => {
  try {
    const monitor = getWebSocketMonitor()
    const sessionManager = getSessionManager()
    const connectionRecovery = getConnectionRecovery()

    // Get basic health status with fallbacks
    const healthStatus = await monitor.getHealthStatus().catch((error) => {
      console.warn('[Health Check] WebSocket monitor failed:', error)
      return {
        status: 'degraded',
        details: { activeSessions: 0, errorRate: 0 },
        checks: { sessions: false, memory: true, errors: false },
      }
    })

    const sessionManagerHealth = await sessionManager.healthCheck().catch((error) => {
      console.warn('[Health Check] Session manager failed:', error)
      return { redis: false, fallback: true, sessions: 0 }
    })

    const recoveryMetrics = connectionRecovery.getRecoveryMetrics()

    // Get system metrics
    const memoryUsage = process.memoryUsage()
    const uptime = process.uptime()

    // Calculate derived metrics
    const successRate = recoveryMetrics.totalRecoveries > 0
      ? (recoveryMetrics.successfulRecoveries / recoveryMetrics.totalRecoveries) * 100
      : 100

    const response = {
      status: healthStatus.status,
      timestamp: new Date().toISOString(),
      uptime,

      // Core health checks
      checks: {
        websocket: healthStatus.status === 'healthy',
        redis: sessionManagerHealth.redis,
        sessions: healthStatus.checks.sessions,
        memory: healthStatus.checks.memory,
        errors: healthStatus.checks.errors,
      },

      // Detailed metrics
      metrics: {
        websocket: {
          activeSessions: healthStatus.details.activeSessions,
          errorRate: healthStatus.details.errorRate,
          connectionSuccessRate: successRate,
        },

        redis: {
          connected: sessionManagerHealth.redis,
          fallbackActive: sessionManagerHealth.fallback,
          sessionCount: sessionManagerHealth.sessions,
        },

        recovery: {
          totalRecoveries: recoveryMetrics.totalRecoveries,
          successfulRecoveries: recoveryMetrics.successfulRecoveries,
          failedRecoveries: recoveryMetrics.failedRecoveries,
          averageRecoveryTime: recoveryMetrics.averageRecoveryTime,
          successRate,
        },

        system: {
          memoryUsagePercent: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
          memoryUsed: memoryUsage.heapUsed,
          memoryTotal: memoryUsage.heapTotal,
          pid: process.pid,
          nodeVersion: process.version,
        },
      },

      // Service information
      service: {
        name: 'websocket-api',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        serverInstance: process.env.SERVER_INSTANCE_ID || `ws-${process.pid}`,
      },
    }

    // Set appropriate HTTP status based on health
    switch (healthStatus.status) {
      case 'healthy':
        setResponseStatus(event, 200)
        break
      case 'degraded':
        setResponseStatus(event, 200) // Still operational
        break
      case 'unhealthy':
        setResponseStatus(event, 503) // Service unavailable
        break
      default:
        setResponseStatus(event, 503) // Default to unhealthy if unknown status
        break
    }

    return response
  }
  catch (error) {
    console.error('[Health Check] Error getting health status:', error)

    setResponseStatus(event, 503)
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    }
  }
})
