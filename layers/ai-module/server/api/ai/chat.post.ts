/**
 * AI Chat API Endpoint - POST /api/ai/chat
 *
 * A comprehensive server endpoint for handling AI chat requests with multi-provider
 * support, authentication, rate limiting, cost monitoring, and streaming responses.
 * Designed to provide secure and scalable AI chat functionality with tool calling.
 *
 * Features:
 * - Multi-provider AI support (OpenAI, Anthropic, Google, XAI, Ollama)
 * - Server-side session authentication with workspace validation
 * - Rate limiting and cost monitoring with configurable limits
 * - Input sanitization and validation for security
 * - Streaming text responses with AI SDK v4.x compatibility
 * - Tool calling for structured outputs (weather, documents, code)
 * - MCP (Model Context Protocol) integration when available
 * - Usage metrics recording and performance monitoring
 * - Comprehensive error handling and debugging headers
 *
 * Architecture:
 * - Uses defineLazyEventHandler for optimized cold starts
 * - Integrates with auth-module session management
 * - Implements security-first design with input validation
 * - Leverages AI SDK for provider abstraction
 * - Records detailed usage metrics for billing and monitoring
 *
 * Security Features:
 * - Server-side authentication validation
 * - Workspace-scoped API key access
 * - Input sanitization against injection attacks
 * - Rate limiting with IP and user tracking
 * - Cost monitoring with configurable limits
 * - Session-based user context (not request body)
 *
 * Performance Considerations:
 * - Lazy event handler initialization
 * - Streaming responses for real-time feedback
 * - Efficient token estimation for rate limiting
 * - Async usage recording without blocking response
 * - Optimized provider model loading
 *
 * Tool Support:
 * - Weather information with forecasting
 * - Document creation and display
 * - Code highlighting and display
 * - Extensible MCP tool integration
 *
 * Request Flow:
 * 1. Authentication and session validation
 * 2. Input sanitization and validation
 * 3. Rate limiting and cost checks
 * 4. Integration and API key resolution
 * 5. Model selection and configuration
 * 6. Tool preparation and MCP integration
 * 7. AI model streaming with tool calling
 * 8. Usage recording and performance metrics
 *
 * @endpoint POST /api/ai/chat
 * @authentication Required (server-side session)
 * @ratelimit Per user and workspace limits
 * @returns Streaming text response with tool calls
 */
import { anthropic } from '@ai-sdk/anthropic'
import { google } from '@ai-sdk/google'
import { openai } from '@ai-sdk/openai'
import { xai } from '@ai-sdk/xai'
import { streamText, tool } from 'ai'
import { getRequestIP } from 'h3'
import { createOllama } from 'ollama-ai-provider'
import { z } from 'zod'
import { getUserSession } from '../../../../auth-module/server/utils/session'
import { calculateRequestCost } from '../../utils/cost-monitor'
import { sanitizeConfig, sanitizeMessages } from '../../utils/input-sanitizer'
import { getIntegrationWithApiKey } from '../../utils/integrations'
import { getMCPTools, isMCPAvailable } from '../../utils/mcp'
import { checkApiLimits, recordApiUsage } from '../../utils/rate-limit-middleware'

export default defineLazyEventHandler(async () => {
  const runtimeConfig = useRuntimeConfig()

  return defineEventHandler(async (event) => {
    const startTime = Date.now()

    try {
      // CHECK AUTHENTICATION FIRST - LEVERAGE AUTH MODULE PATTERN
      const session = await getUserSession(event)

      if (!session || !session.isAuthenticated) {
        throw createError({
          statusCode: 401,
          statusMessage: 'Unauthorized - Please log in',
        })
      }

      // EXTRACT USER CONTEXT FROM SESSION - NOT FROM REQUEST BODY
      const userId = session.user?.id
      const workspaceId = session.currentWorkspace?.id

      if (!userId || !workspaceId) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid session - missing user or workspace context',
        })
      }

      // Get client information
      const clientIp = getRequestIP(event) || '127.0.0.1'
      const userAgent = getHeader(event, 'user-agent') || 'unknown'

      // Read and parse request body (remove userId/workspaceId extraction)
      const requestBody = await readBody(event)
      const { messages, config } = requestBody

      // Debug logging for request tracking
      if (process.env.NODE_ENV === 'development') {
        console.warn('[AI Chat] Received request:', {
          messagesCount: messages?.length,
          config: config ? { provider: config.provider, model: config.model } : undefined,
          workspaceId,
          userId,
          clientIp,
        })
      }

      // Input sanitization and validation
      if (!messages || !config) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Missing required fields: messages, config',
        })
      }

      // Sanitize messages
      const messageSanitization = sanitizeMessages(messages)
      if (!messageSanitization.isValid) {
        throw createError({
          statusCode: 400,
          statusMessage: `Invalid messages: ${messageSanitization.errors.join(', ')}`,
        })
      }

      // Sanitize config
      const configSanitization = sanitizeConfig(config)
      if (!configSanitization.isValid) {
        throw createError({
          statusCode: 400,
          statusMessage: `Invalid config: ${configSanitization.errors.join(', ')}`,
        })
      }

      const sanitizedMessages = messageSanitization.sanitizedData
      const sanitizedConfig = configSanitization.sanitizedData

      // Estimate token usage for rate limiting
      const estimatedTokens = estimateTokens(sanitizedMessages, sanitizedConfig.systemPrompt)
      const estimatedCost = await calculateRequestCost(
        sanitizedConfig.provider,
        sanitizedConfig.model,
        Math.floor(estimatedTokens * 0.7), // Estimated prompt tokens
        Math.floor(estimatedTokens * 0.3), // Estimated completion tokens
      )

      // Rate limiting and cost checks with verified session data
      const rateLimitContext = {
        userId, // From verified session
        workspaceId, // From verified session
        ip: clientIp,
        endpoint: '/api/ai/chat',
        provider: sanitizedConfig.provider,
        model: sanitizedConfig.model,
        userAgent,
        isAuthenticated: true, // Verified server-side
      }

      const limitResult = await checkApiLimits(rateLimitContext, estimatedTokens, estimatedCost)

      // Set rate limit headers
      Object.entries(limitResult.headers).forEach(([key, value]) => {
        setHeader(event, key, value)
      })

      if (!limitResult.allowed) {
        const statusCode = limitResult.rateLimitResult?.allowed === false ? 429 : 402
        const message = limitResult.error
          || limitResult.costLimitResult?.reason
          || 'Request blocked by rate limiting'

        throw createError({
          statusCode,
          statusMessage: message,
        })
      }

      // Get integration and decrypt API key
      const integration = await getIntegrationWithApiKey(sanitizedConfig.integrationId, workspaceId, event)
      if (!integration || !integration.credentials?.apiKey) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid integration or missing API key',
        })
      }

      // Get the appropriate model based on provider
      const model = getModel(sanitizedConfig.provider, sanitizedConfig.model, integration.credentials.apiKey, runtimeConfig)
      if (!model) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Unsupported provider or model',
        })
      }

      // Convert messages to AI SDK format
      const aiMessages = sanitizedMessages.map((msg: any) => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content,
      }))

      // Add system prompt if configured
      if (sanitizedConfig.systemPrompt) {
        aiMessages.unshift({
          role: 'system',
          content: sanitizedConfig.systemPrompt,
        })
      }

      // Define tools for structured outputs
      let tools = {
        weather: tool({
          description: 'Get weather information for a location',
          parameters: z.object({
            location: z.string().describe('The location to get weather for'),
            temperature: z.number().describe('Current temperature in Celsius'),
            condition: z.string().describe('Weather condition (e.g., sunny, cloudy, rainy)'),
            humidity: z.number().optional().describe('Humidity percentage'),
            windSpeed: z.number().optional().describe('Wind speed in km/h'),
            forecast: z.array(z.object({
              date: z.string(),
              high: z.number(),
              low: z.number(),
              condition: z.string(),
            })).optional().describe('3-day forecast'),
          }),
          execute: async (args) => {
            return {
              type: 'weather',
              ...args,
            }
          },
        }),

        document: tool({
          description: 'Create or display a document',
          parameters: z.object({
            title: z.string().describe('Document title'),
            content: z.string().describe('Document content'),
            format: z.enum(['markdown', 'text', 'html']).default('markdown'),
            editable: z.boolean().optional().default(true),
          }),
          execute: async (args) => {
            return {
              type: 'document',
              format: args.format,
              content: args.content,
              editable: args.editable,
              title: args.title,
            }
          },
        }),

        code: tool({
          description: 'Display code with syntax highlighting',
          parameters: z.object({
            title: z.string().describe('Title or description of the code'),
            language: z.string().describe('Programming language'),
            code: z.string().describe('The code to display'),
            filename: z.string().optional().describe('Optional filename'),
          }),
          execute: async (args) => {
            return {
              type: 'code',
              language: args.language,
              code: args.code,
              filename: args.filename,
              title: args.title,
            }
          },
        }),
      }

      // Add MCP tools if available
      if (isMCPAvailable() && sanitizedConfig.enableMCP !== false) {
        try {
          const mcpTools = await getMCPTools()
          tools = { ...tools, ...mcpTools }
        }
        catch (error) {
          console.warn('[AI Chat] Failed to load MCP tools:', error)
        }
      }

      // Stream the response with tools
      const result = streamText({
        model,
        messages: aiMessages,
        temperature: sanitizedConfig.temperature || 0.7,
        maxTokens: sanitizedConfig.maxTokens,
        topP: sanitizedConfig.topP,
        frequencyPenalty: sanitizedConfig.frequencyPenalty,
        presencePenalty: sanitizedConfig.presencePenalty,
        tools,
        toolChoice: 'auto',
        maxSteps: 5,
      })

      // Record usage metrics after successful response
      result.usage.then(async (usage) => {
        if (usage) {
          const actualCost = await calculateRequestCost(
            sanitizedConfig.provider,
            sanitizedConfig.model,
            usage.promptTokens,
            usage.completionTokens,
          )

          await recordApiUsage(rateLimitContext, {
            promptTokens: usage.promptTokens,
            completionTokens: usage.completionTokens,
            cost: actualCost,
            provider: sanitizedConfig.provider,
            model: sanitizedConfig.model,
          })

          // Usage metrics recorded successfully
        }
      }).catch((error) => {
        console.error('[AI Chat] Failed to record usage:', error)
      })

      // Add performance headers
      setHeader(event, 'X-Response-Time', `${Date.now() - startTime}ms`)
      setHeader(event, 'X-Provider', sanitizedConfig.provider)
      setHeader(event, 'X-Model', sanitizedConfig.model)

      // Return the streaming response with tool support (AI SDK v4.x compatible)
      // Use toDataStreamResponse() for tool calling support with useChat composable
      return result.toDataStreamResponse({
        getErrorMessage: (error: unknown) => {
          if (error instanceof Error) {
            return error.message
          }
          return 'An error occurred during streaming'
        },
      })
    }
    catch (error) {
      console.error('[AI Chat] Error:', error)

      // Add error headers for debugging
      setHeader(event, 'X-Error-Time', `${Date.now() - startTime}ms`)

      // Type-safe error handling
      const typedError = error as any
      throw createError({
        statusCode: typedError.statusCode || 500,
        statusMessage: typedError.statusMessage || (error instanceof Error ? error.message : 'Failed to generate response'),
      })
    }
  })
})

/**
 * Get model instance based on provider
 *
 * Creates and configures AI model instances for different providers using the AI SDK.
 * Handles provider-specific configuration and API key management through direct
 * parameter passing for secure, per-request authentication.
 *
 * Supported Providers:
 * - OpenAI: GPT models with chat and completion endpoints
 * - Anthropic: Claude models with messages API
 * - Google: Gemini models with Generative AI API
 * - XAI: Grok models with chat API
 * - Ollama: Self-hosted models with configurable base URL
 *
 * Model Instantiation:
 * - Uses provider-specific SDK functions with direct API key passing
 * - Supports custom endpoints for Ollama
 * - Returns null for unsupported providers
 * - Thread-safe implementation without environment variable mutation
 *
 * Complexity: O(1) - Direct provider lookup and instantiation
 *
 * @param {string} provider - The AI provider name (lowercase)
 * @param {string} modelName - The specific model identifier
 * @param {string} apiKey - The API key for the provider
 * @param {any} runtimeConfig - Nuxt runtime configuration object
 * @returns {any | null} Configured model instance or null if provider unsupported
 *
 * @example
 * const model = getModel('openai', 'gpt-4-turbo', apiKey, runtimeConfig)
 * if (model) {
 *   const response = await streamText({ model, messages })
 * }
 */
function getModel(provider: string, modelName: string, apiKey: string, runtimeConfig: any) {
  switch (provider) {
    case 'openai':
      return openai(modelName, { apiKey })

    case 'anthropic':
      return anthropic(modelName, { apiKey })

    case 'google':
      return google(modelName, { apiKey })

    case 'xai':
      return xai(modelName, { apiKey })

    case 'ollama': {
      // For Ollama, use the base URL from runtime config or default
      const baseURL = runtimeConfig.public.ollamaBaseUrl || 'http://localhost:11434/v1'
      const ollama = createOllama({ baseURL })
      return ollama(modelName)
    }

    default:
      return null
  }
}

/**
 * Estimate token count for a conversation
 *
 * Provides a fast approximation of token usage for rate limiting and cost
 * estimation purposes. Uses character-based estimation with empirical ratios
 * derived from common LLM tokenization patterns.
 *
 * Estimation Method:
 * - Counts total character length of all messages and system prompt
 * - Applies 4:1 character-to-token ratio (standard for English text)
 * - Adds 20% buffer for formatting tokens, special tokens, and model overhead
 * - Rounds up to ensure conservative estimates for rate limiting
 *
 * Accuracy Considerations:
 * - Optimized for English text (other languages may vary)
 * - Conservative estimates to prevent limit overruns
 * - Does not account for provider-specific tokenization differences
 * - Suitable for rate limiting but not precise billing
 *
 * Performance:
 * - O(n) where n is total number of messages
 * - Fast string operations for real-time processing
 * - No external API calls or complex tokenization
 *
 * Complexity: O(n*m) where n is number of messages and m is average message length
 *
 * @param {any[]} messages - Array of conversation messages with content property
 * @param {string} [systemPrompt] - Optional system prompt to include in count
 * @returns {number} Estimated token count with safety buffer
 *
 * @example
 * const messages = [
 *   { content: "Hello, how are you?" },
 *   { content: "I'm doing well, thank you!" }
 * ]
 * const tokens = estimateTokens(messages, "You are a helpful assistant")
 * console.log(`Estimated tokens: ${tokens}`) // ~15 tokens with buffer
 */
function estimateTokens(messages: any[], systemPrompt?: string): number {
  let totalLength = 0

  // Add system prompt length
  if (systemPrompt) {
    totalLength += systemPrompt.length
  }

  // Add message lengths
  for (const message of messages) {
    totalLength += (message.content || '').length
  }

  // Rough estimate: 1 token per 4 characters for English text
  // Add 20% buffer for formatting and special tokens
  return Math.ceil((totalLength / 4) * 1.2)
}
