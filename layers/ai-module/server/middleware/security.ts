import type { H3Event } from 'h3'
import { getRequestIP } from 'h3'
import { getSecurityHeaders, rateLimiter, securityLogger } from '../../utils/security'

// Security middleware for AI API endpoints
export async function securityMiddleware(event: H3Event) {
  // Apply security headers
  const headers = getSecurityHeaders()
  for (const [key, value] of Object.entries(headers)) {
    setHeader(event, key, value)
  }

  // Get client identifier for rate limiting
  const clientIp = getRequestIP(event) || 'unknown'
  const userAgent = getHeader(event, 'user-agent') || 'unknown'
  const clientId = `${clientIp}-${userAgent.substring(0, 50)}`

  // Rate limiting - 100 requests per hour per client
  if (!rateLimiter.isAllowed(clientId, 100, 60 * 60 * 1000)) {
    securityLogger.log({
      type: 'rate_limit_exceeded',
      severity: 'medium',
      source: 'security_middleware',
      details: { clientIp, userAgent, endpoint: event.node.req.url },
      ip: clientIp,
    })

    throw createError({
      statusCode: 429,
      statusMessage: 'Too Many Requests',
    })
  }

  // Content-Type validation for POST requests only
  // GET requests don't need content-type validation
  if (event.node.req.method === 'POST') {
    const contentType = getHeader(event, 'content-type')
    if (!contentType || !contentType.includes('application/json')) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid Content-Type. Expected application/json',
      })
    }
  }

  // Request size validation
  const contentLength = getHeader(event, 'content-length')
  if (contentLength && Number.parseInt(contentLength) > 10 * 1024 * 1024) { // 10MB limit
    securityLogger.log({
      type: 'validation_failed',
      severity: 'medium',
      source: 'security_middleware',
      details: { reason: 'Request too large', size: contentLength },
      ip: clientIp,
    })

    throw createError({
      statusCode: 413,
      statusMessage: 'Request Entity Too Large',
    })
  }

  // Log security-relevant events
  if (event.node.req.url?.includes('/api/ai/')) {
    securityLogger.log({
      type: 'input_sanitized',
      severity: 'low',
      source: 'api_access',
      details: {
        endpoint: event.node.req.url,
        method: event.node.req.method,
        userAgent: userAgent.substring(0, 100),
      },
      ip: clientIp,
    })
  }
}

// Authentication middleware
export async function authenticationMiddleware(event: H3Event) {
  // Skip auth for OPTIONS requests
  if (event.node.req.method === 'OPTIONS') {
    return
  }

  const authHeader = getHeader(event, 'authorization')
  if (!authHeader) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Missing authorization header',
    })
  }

  // Validate Bearer token format
  if (!authHeader.startsWith('Bearer ')) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Invalid authorization header format',
    })
  }

  const token = authHeader.substring(7)
  if (!token || token.length < 10) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Invalid token format',
    })
  }

  // In a real implementation, validate the token against your auth system
  // For now, we'll just store it in context for later validation
  event.context.authToken = token
}

// CSRF protection middleware
export async function csrfProtectionMiddleware(event: H3Event) {
  // Only apply CSRF protection to state-changing operations
  if (!['POST', 'PUT', 'PATCH', 'DELETE'].includes(event.node.req.method!)) {
    return
  }

  const csrfToken = getHeader(event, 'x-csrf-token')
  const sessionToken = getCookie(event, 'session-token')

  if (!csrfToken || !sessionToken) {
    throw createError({
      statusCode: 403,
      statusMessage: 'CSRF token required',
    })
  }

  // In a real implementation, validate CSRF token against session
  // For now, we'll just check they exist and have proper format
  if (csrfToken.length !== 36 || sessionToken.length < 10) {
    securityLogger.log({
      type: 'validation_failed',
      severity: 'high',
      source: 'csrf_protection',
      details: { reason: 'Invalid CSRF token format' },
      ip: getRequestIP(event),
    })

    throw createError({
      statusCode: 403,
      statusMessage: 'Invalid CSRF token',
    })
  }
}

// Input validation middleware
export async function inputValidationMiddleware(event: H3Event) {
  if (event.node.req.method !== 'POST') {
    return
  }

  try {
    const body = await readBody(event)

    // Validate body exists and is an object
    if (!body || typeof body !== 'object') {
      throw new Error('Request body must be a valid JSON object')
    }

    // Check for common injection patterns in all string values
    const checkForInjection = (value: any): void => {
      if (typeof value === 'string') {
        // Check for SQL injection - more specific patterns
        if (/(\bunion\s+select\b|\bselect\s+.*\s+from\s+|\binsert\s+into\s+|\bdelete\s+from\s+|\bupdate\s+.*\s+set\s+|\bdrop\s+table\s+)/i.test(value)) {
          throw new Error('Potential SQL injection detected')
        }

        // Check for script injection
        if (/<script[^>]*>.*?<\/script>/i.test(value)) {
          throw new Error('Script injection detected')
        }

        // Check for command injection - more specific patterns
        if (/([|&;`]\s*(rm|del|format|shutdown|reboot|kill|chmod|sudo)|\$\(.*\)|\$\{.*\})/i.test(value)) {
          throw new Error('Potential command injection detected')
        }
      }
      else if (typeof value === 'object' && value !== null) {
        Object.values(value).forEach(checkForInjection)
      }
    }

    checkForInjection(body)

    // Store validated body
    event.context.validatedBody = body
  }
  catch (error) {
    securityLogger.log({
      type: 'validation_failed',
      severity: 'high',
      source: 'input_validation',
      details: {
        reason: error instanceof Error ? error.message : 'Unknown validation error',
        endpoint: event.node.req.url,
      },
      ip: getRequestIP(event),
    })

    throw createError({
      statusCode: 400,
      statusMessage: error instanceof Error ? error.message : 'Invalid request data',
    })
  }
}

// Audit logging middleware
export async function auditLoggingMiddleware(event: H3Event) {
  const startTime = Date.now()
  const clientIp = getRequestIP(event)
  const userAgent = getHeader(event, 'user-agent')
  const userId = event.context.userId
  const workspaceId = event.context.workspaceId

  // Log request start
  securityLogger.log({
    type: 'input_sanitized',
    severity: 'low',
    source: 'audit_log',
    details: {
      event: 'request_start',
      method: event.node.req.method,
      url: event.node.req.url,
      userAgent: userAgent?.substring(0, 100),
    },
    userId,
    workspaceId,
    ip: clientIp,
  })

  // Hook into response to log completion
  event.node.res.on('finish', () => {
    const duration = Date.now() - startTime
    const statusCode = event.node.res.statusCode

    securityLogger.log({
      type: 'input_sanitized',
      severity: statusCode >= 400 ? 'medium' : 'low',
      source: 'audit_log',
      details: {
        event: 'request_complete',
        method: event.node.req.method,
        url: event.node.req.url,
        statusCode,
        duration,
      },
      userId,
      workspaceId,
      ip: clientIp,
    })
  })
}

// Combined security middleware
export default defineEventHandler(async (event) => {
  // Skip middleware for non-API routes
  if (!event.node.req.url?.startsWith('/api/ai/')) {
    return
  }

  try {
    // Apply security middleware in order
    await securityMiddleware(event)
    await inputValidationMiddleware(event)
    await auditLoggingMiddleware(event)

    // Note: Authentication and CSRF are handled per-endpoint as needed
  }
  catch (error) {
    // Log security errors
    securityLogger.log({
      type: 'validation_failed',
      severity: 'high',
      source: 'security_middleware',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: event.node.req.url,
      },
      ip: getRequestIP(event),
    })

    throw error
  }
})
