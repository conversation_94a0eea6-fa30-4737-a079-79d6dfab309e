#!/usr/bin/env node

/**
 * Dev Workflow Integration Test
 * Tests the core functionality of the /dev command system
 * Validates component integration and basic workflow functionality
 */

const { execSync } = require('node:child_process')
const fs = require('node:fs')
const path = require('node:path')

// Test configuration
const TEST_CONFIG = {
  claudeDir: path.join(process.cwd(), '.claude'),
  devWorkflowsDir: path.join(process.cwd(), '.claude', 'state', 'dev-workflows'),
  hooksDir: path.join(process.cwd(), '.claude', 'hooks'),
  testTimeout: 30000, // 30 seconds
}

// Test utilities
class DevWorkflowTester {
  constructor() {
    this.testResults = []
    this.testsPassed = 0
    this.testsFailed = 0
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'
    console.log(`[${timestamp}] ${prefix} ${message}`)
  }

  assert(condition, testName, errorMessage = '') {
    if (condition) {
      this.testsPassed++
      this.testResults.push({ test: testName, status: 'PASS' })
      this.log(`${testName}: PASS`, 'success')
    }
    else {
      this.testsFailed++
      this.testResults.push({ test: testName, status: 'FAIL', error: errorMessage })
      this.log(`${testName}: FAIL - ${errorMessage}`, 'error')
    }
  }

  // Test 1: Verify directory structure
  testDirectoryStructure() {
    this.log('Testing directory structure...')

    // Check core directories exist
    this.assert(
      fs.existsSync(TEST_CONFIG.claudeDir),
      'Claude directory exists',
      '.claude directory not found',
    )

    this.assert(
      fs.existsSync(TEST_CONFIG.devWorkflowsDir),
      'Dev workflows state directory exists',
      'Dev workflows state directory not found',
    )

    this.assert(
      fs.existsSync(TEST_CONFIG.hooksDir),
      'Hooks directory exists',
      'Hooks directory not found',
    )

    // Check specific files exist
    const requiredFiles = [
      '.claude/commands/workflows/dev-command.md',
      '.claude/commands/orchestration/dev-orchestrator.md',
      '.claude/hooks/dev-workflow-enhancer.sh',
      '.claude/state/dev-workflows/README.md',
      'bmad-agent/templates/dev-work-specification.md',
      'bmad-agent/config/mpc-capabilities.yml',
    ]

    requiredFiles.forEach((file) => {
      const filePath = path.join(process.cwd(), file)
      this.assert(
        fs.existsSync(filePath),
        `Required file exists: ${file}`,
        `File not found: ${filePath}`,
      )
    })
  }

  // Test 2: Verify hook executability
  testHookExecutability() {
    this.log('Testing hook executability...')

    const hooks = [
      '.claude/hooks/dev-workflow-enhancer.sh',
      '.claude/hooks/agent-orchestration-hook.sh',
    ]

    hooks.forEach((hookPath) => {
      const fullPath = path.join(process.cwd(), hookPath)
      try {
        const stats = fs.statSync(fullPath)
        const isExecutable = !!(stats.mode & Number.parseInt('111', 8))
        this.assert(
          isExecutable,
          `Hook is executable: ${hookPath}`,
          `Hook not executable: ${fullPath}`,
        )
      }
      catch (error) {
        this.assert(
          false,
          `Hook file accessible: ${hookPath}`,
          `Cannot access hook: ${error.message}`,
        )
      }
    })
  }

  // Test 3: Verify configuration structure
  testConfigurationStructure() {
    this.log('Testing configuration structure...')

    const configPath = path.join(process.cwd(), 'bmad-agent/config/mpc-capabilities.yml')
    try {
      const configContent = fs.readFileSync(configPath, 'utf8')

      // Check for key configuration sections
      const requiredSections = [
        'dev_workflow_integration',
        'context_engineering',
        'agent_enhancement',
        'workflow_automation',
        'quality_enhancement',
        'dev_command_patterns',
      ]

      requiredSections.forEach((section) => {
        this.assert(
          configContent.includes(section),
          `Configuration contains section: ${section}`,
          `Missing configuration section: ${section}`,
        )
      })

      // Check for MCP tool configurations
      const mcpTools = ['context7', 'zen', 'perplexity', 'firecrawl', 'playwright']
      mcpTools.forEach((tool) => {
        this.assert(
          configContent.includes(tool),
          `MCP tool configured: ${tool}`,
          `Missing MCP tool configuration: ${tool}`,
        )
      })
    }
    catch (error) {
      this.assert(
        false,
        'Configuration file readable',
        `Cannot read configuration: ${error.message}`,
      )
    }
  }

  // Test 4: Verify workflow template structure
  testWorkflowTemplate() {
    this.log('Testing workflow template structure...')

    const templatePath = path.join(process.cwd(), 'bmad-agent/templates/dev-work-specification.md')
    try {
      const templateContent = fs.readFileSync(templatePath, 'utf8')

      // Check for key template sections
      const requiredSections = [
        '# Development Work Specification Template',
        '## Metadata',
        '## Requirements Analysis',
        '## Technical Context',
        '## Quality Assurance Framework',
        '## Implementation Plan',
        '## Context Enhancement',
        '## Completion Criteria',
      ]

      requiredSections.forEach((section) => {
        this.assert(
          templateContent.includes(section),
          `Template contains section: ${section}`,
          `Missing template section: ${section}`,
        )
      })

      // Check for LEVER framework integration
      const leverElements = ['Leverage', 'Extend', 'Verify', 'Eliminate', 'Reduce']
      leverElements.forEach((element) => {
        this.assert(
          templateContent.includes(element),
          `Template includes LEVER element: ${element}`,
          `Missing LEVER element: ${element}`,
        )
      })
    }
    catch (error) {
      this.assert(
        false,
        'Workflow template readable',
        `Cannot read template: ${error.message}`,
      )
    }
  }

  // Test 5: Verify state management structure
  testStateManagement() {
    this.log('Testing state management structure...')

    const stateReadmePath = path.join(TEST_CONFIG.devWorkflowsDir, 'README.md')
    try {
      const readmeContent = fs.readFileSync(stateReadmePath, 'utf8')

      // Check for key state management concepts
      const stateElements = [
        'State Structure',
        'Workflow Lifecycle State',
        'Context Engineering State',
        'Agent Context Package',
        'Recovery and Fault Tolerance',
      ]

      stateElements.forEach((element) => {
        this.assert(
          readmeContent.includes(element),
          `State documentation includes: ${element}`,
          `Missing state element: ${element}`,
        )
      })
    }
    catch (error) {
      this.assert(
        false,
        'State management documentation readable',
        `Cannot read state documentation: ${error.message}`,
      )
    }
  }

  // Test 6: Test hook integration
  testHookIntegration() {
    this.log('Testing hook integration...')

    const orchestrationHookPath = path.join(process.cwd(), '.claude/hooks/agent-orchestration-hook.sh')
    try {
      const hookContent = fs.readFileSync(orchestrationHookPath, 'utf8')

      // Check for dev workflow integration
      this.assert(
        hookContent.includes('DEV_WORKFLOW_ENHANCER'),
        'Agent orchestration hook includes dev workflow integration',
        'Missing dev workflow integration in agent orchestration hook',
      )

      this.assert(
        hookContent.includes('dev-workflow-enhancer.sh'),
        'Agent orchestration hook references dev workflow enhancer',
        'Missing reference to dev workflow enhancer',
      )
    }
    catch (error) {
      this.assert(
        false,
        'Hook integration verification',
        `Cannot verify hook integration: ${error.message}`,
      )
    }
  }

  // Test 7: Simulate basic workflow creation
  testBasicWorkflowSimulation() {
    this.log('Testing basic workflow simulation...')

    try {
      // Create a test workflow state
      const testWorkflowState = {
        workflowId: `test-${Date.now()}`,
        status: 'analyzing',
        phase: 'requirement-analysis',
        requirement: 'test feature implementation',
        parameters: {
          priority: 'medium',
          focus: 'general',
          agent: 'auto',
          style: 'standard',
        },
        startTime: new Date().toISOString(),
        lastUpdate: new Date().toISOString(),
      }

      const testStatePath = path.join(TEST_CONFIG.devWorkflowsDir, 'test-workflow-state.json')
      fs.writeFileSync(testStatePath, JSON.stringify(testWorkflowState, null, 2))

      // Verify file was created and is readable
      this.assert(
        fs.existsSync(testStatePath),
        'Test workflow state file created',
        'Failed to create test workflow state file',
      )

      const readState = JSON.parse(fs.readFileSync(testStatePath, 'utf8'))
      this.assert(
        readState.workflowId === testWorkflowState.workflowId,
        'Test workflow state readable and valid',
        'Test workflow state corrupted or invalid',
      )

      // Clean up test file
      fs.unlinkSync(testStatePath)
    }
    catch (error) {
      this.assert(
        false,
        'Basic workflow simulation',
        `Workflow simulation failed: ${error.message}`,
      )
    }
  }

  // Test 8: Verify documentation completeness
  testDocumentationCompleteness() {
    this.log('Testing documentation completeness...')

    const documentationFiles = [
      '.claude/commands/workflows/dev-command.md',
      '.claude/commands/orchestration/dev-orchestrator.md',
      'test/dev-command-validation.md',
    ]

    documentationFiles.forEach((docFile) => {
      const filePath = path.join(process.cwd(), docFile)
      try {
        const content = fs.readFileSync(filePath, 'utf8')

        // Check for essential documentation elements
        this.assert(
          content.length > 1000,
          `Documentation substantial: ${docFile}`,
          `Documentation too brief: ${docFile}`,
        )

        this.assert(
          content.includes('Usage') || content.includes('Purpose'),
          `Documentation includes usage: ${docFile}`,
          `Missing usage information: ${docFile}`,
        )
      }
      catch (error) {
        this.assert(
          false,
          `Documentation accessible: ${docFile}`,
          `Cannot access documentation: ${error.message}`,
        )
      }
    })
  }

  // Run all tests
  async runAllTests() {
    this.log('Starting Dev Workflow Integration Tests...')

    try {
      this.testDirectoryStructure()
      this.testHookExecutability()
      this.testConfigurationStructure()
      this.testWorkflowTemplate()
      this.testStateManagement()
      this.testHookIntegration()
      this.testBasicWorkflowSimulation()
      this.testDocumentationCompleteness()

      // Generate test report
      this.generateTestReport()
    }
    catch (error) {
      this.log(`Test execution failed: ${error.message}`, 'error')
      process.exit(1)
    }
  }

  // Generate comprehensive test report
  generateTestReport() {
    this.log('Generating test report...')

    const totalTests = this.testsPassed + this.testsFailed
    const successRate = totalTests > 0 ? ((this.testsPassed / totalTests) * 100).toFixed(2) : 0

    const report = {
      summary: {
        totalTests,
        passed: this.testsPassed,
        failed: this.testsFailed,
        successRate: `${successRate}%`,
      },
      timestamp: new Date().toISOString(),
      results: this.testResults,
    }

    // Write report to file
    const reportPath = path.join(process.cwd(), 'test', 'dev-workflow-test-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    // Console summary
    this.log(`\n=== TEST SUMMARY ===`)
    this.log(`Total Tests: ${totalTests}`)
    this.log(`Passed: ${this.testsPassed}`, 'success')
    this.log(`Failed: ${this.testsFailed}`, this.testsFailed > 0 ? 'error' : 'info')
    this.log(`Success Rate: ${successRate}%`)
    this.log(`Report saved to: ${reportPath}`)

    if (this.testsFailed > 0) {
      this.log('Some tests failed. Please review the failures above.', 'error')
      process.exit(1)
    }
    else {
      this.log('All tests passed! Dev workflow system is ready for use.', 'success')
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new DevWorkflowTester()
  tester.runAllTests().catch((error) => {
    console.error('Test execution failed:', error)
    process.exit(1)
  })
}

module.exports = DevWorkflowTester
