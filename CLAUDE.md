# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
DO NOT REMOVE THE MUST READ:
MUST read `(project-root)/pib-agent/ide-pib-orchestrator.md` on startup
## Project Overview

This is a Tairo Nuxt application with an integrated PIB (Breakthrough Method of Agile AI-driven Development) agent system. The project combines a modern Nuxt 3 frontend with a sophisticated multi-agent orchestration framework for AI-powered development workflows.

## Development Commands

### Primary Commands
```bash
pnpm install      # Install all dependencies (uses pnpm workspaces)
pnpm dev          # Start development server (opens automatically)
pnpm build        # Build for production
pnpm generate     # Generate static site
pnpm lint         # Run ESLint with auto-fix
pnpm typecheck    # Run TypeScript type checking
pnpm test         # Run all tests (currently runs TypeScript checking and linting)
```

### Workspace-Specific Commands
```bash
pnpm demo:dev     # Run demo application
pnpm demo:build   # Build demo application
pnpm clean:all    # Clean all build artifacts and node_modules

# Run commands for specific workspaces
pnpm --filter=@pib/auth-module test    # Run tests for auth module
pnpm --filter=./apps/pib dev           # Run specific app
```

### Firebase Development Commands
```bash
pnpm emulators           # Start Firebase emulators (Auth, Firestore, Storage, Functions)
pnpm emulators-export    # Export Firebase emulator data
pnpm deploy:rules        # Deploy Firestore security rules
pnpm deploy:indexes      # Deploy Firestore indexes
pnpm deploy:functions    # Deploy Cloud Functions
```

### Module Testing Commands
When working on specific modules (e.g., auth-module):
```bash
cd layers/auth-module
pnpm test:unit           # Run unit tests
pnpm test:integration    # Run integration tests
pnpm test:e2e           # Run end-to-end tests with Playwright
pnpm test:security      # Run security audit tests
pnpm test:performance   # Run performance benchmarks
pnpm test:ui            # Run tests with Vitest UI interface
pnpm coverage           # Generate test coverage report
```

## Architecture Overview

### Project Structure
The codebase follows a monorepo structure with pnpm workspaces:

```
/
├── apps/               # Individual applications
│   ├── pib/           # Main PIB application
│   └── app-template/  # Template for new apps
├── layers/            # Reusable feature modules
│   ├── tairo/         # Core UI layer (components, layouts, composables)
│   ├── auth-module/   # Authentication with Firebase Auth
│   └── module-template/ # Template for new modules
├── bmad-agent/        # AI agent orchestration system
│   ├── personas/      # Agent personality definitions
│   ├── tasks/         # Workflow task definitions
│   ├── templates/     # Document templates
│   └── checklists/    # Quality validation checklists
├── functions/         # Firebase Cloud Functions
├── .app/              # Legacy main application (being phased out)
└── .demo/             # Legacy demo application (being phased out)
```

### Technology Stack
- **Framework**: Nuxt 3 with Vue 3 (Composition API)
- **TypeScript**: Full TypeScript support with strict mode
- **Styling**: Tailwind CSS v4 with LightningCSS
- **UI Components**: Shuriken UI + custom Tairo components
- **Build Tool**: Vite (via Nuxt)
- **Package Manager**: pnpm with workspaces
- **Node Version**: >=22 (required)
- **Backend Services**: Firebase (Auth, Firestore, Storage, Functions)
- **Testing**: Vitest, Vue Test Utils, Playwright
- **Linting**: ESLint with @antfu/eslint-config

### Nuxt Layer Architecture
The application uses Nuxt's layer system:
1. **Base Layer**: `layers/tairo` - provides core UI components and layouts
2. **App Layer**: `.app` - extends Tairo with application-specific code

Key patterns:
- Components are auto-imported from the Tairo layer
- Layouts include: collapse, sidebar, sidenav, topnav
- Composables manage layout state and configuration

### BMAD Agent System
The `bmad-agent/` directory contains a sophisticated AI orchestration system:

**Core Concepts:**
- **Orchestrator**: Central controller managing agent activation
- **Personas**: Specialized AI agents (PM, Architect, Dev, QA, etc.)
- **Tasks**: Structured workflow definitions
- **Templates**: Standardized document outputs
- **MCP Integration**: External AI tool connections

**Agent Activation Pattern:**
1. User requests specific persona + task
2. Orchestrator loads configuration
3. Task executed with persona context
4. Multi-agent workflows for complex tasks

### Component Architecture
Tairo components follow consistent patterns:
- **Naming**: `Tairo` prefix (e.g., `TairoTable`, `TairoSidebar`)
- **Composition**: Smaller focused components composed into layouts
- **State Management**: Composables for cross-component state
- **Styling**: Tailwind utilities with minimal custom CSS
- **Shared Components**: All components and composables that need to be used across multiple modules (like toasters, modals, notifications) should be placed in `layers/tairo/` to ensure availability throughout the application

### Development Patterns
1. **Auto-imports**: Components and composables are auto-imported
2. **TypeScript**: Strict typing throughout the codebase
3. **Composables**: Shared logic in `composables/` directory
4. **Layouts**: Multiple layout options for different app sections
5. **Configuration**: Layer-based configuration inheritance

### Import Guidelines
- **Nuxt 3 Auto-imports**: Many composables (`useAuth`, `useRoute`, etc.) are auto-imported by Nuxt
- **Explicit imports when needed**: If auto-import fails, use explicit imports with relative paths
- **Avoid `~/` prefix**: The `~/` path alias can cause resolution errors in development
- **Preferred import format**: Use relative paths like `'../../layers/auth-module/composables/auth'`
- **Check auto-import first**: Before adding explicit imports, verify if the function is auto-imported

## Module Architecture

The project uses a modular architecture with Nuxt 3 layers:

### Key Modules

#### AI Module (`/layers/ai-module/`)
Advanced AI chat with dynamic components:
- **Dynamic Artifacts**: AI generates interactive weather cards, documents, code blocks
- **Tool Integration**: WebSocket handler uses AI SDK tools for structured outputs
- **Split View Workspace**: Chat + artifact side-by-side viewing
- **Streaming Support**: Real-time content generation
- **Documentation**: See `/layers/ai-module/docs/DYNAMIC_COMPONENTS.md`

### Creating New Modules
1. Copy `/layers/module-template` to `/layers/your-module-name`
2. Update package.json name and nuxt.config.ts metadata
3. Add components, composables, and other module code
4. Modules are automatically available to apps that extend them

### Creating New Apps
1. Copy `/apps/app-template` to `/apps/your-app-name`
2. Update package.json name
3. Configure which modules to include in nuxt.config.ts extends array
4. Run with `cd apps/your-app-name && pnpm dev`

### Module Composition
Apps compose functionality by extending multiple modules:
```typescript
extends: [
  '../../layers/tairo',      // Base UI layer
  '../../layers/auth-module', // Authentication
  '../../layers/your-module'  // Your features
]
```

See `/docs/MODULE_ARCHITECTURE.md` for detailed guide.

## Key Files to Understand

- `/docs/MODULE_ARCHITECTURE.md` - Complete module system guide
- `/layers/tairo/nuxt.config.ts` - Core layer configuration
- `/layers/module-template/` - Template for new modules
- `/apps/app-template/` - Template for new apps
- `/bmad-agent/ide-bmad-orchestrator.cfg.md` - Agent system configuration
- `/bmad-agent/personas/bmad.md` - Master orchestrator definition
- `/layers/tairo/composables/layout-*.ts` - Layout state management

## Data Handling Architecture

The application uses a **hybrid Firebase-first architecture** for data management:

### Frontend Data Handling (Primary)
- **Direct Firebase SDK operations** from the browser using Firestore
- **Real-time data synchronization** through Firestore listeners
- **Client-side state management** using Vue's reactive system
- **Firebase Security Rules** control data access and validation

Use Firebase directly for:
- User authentication and profiles
- Real-time updates (workspaces, notifications)
- Simple CRUD operations
- Data that benefits from real-time sync

### Backend Data Handling (As Needed)
Create Nuxt server routes (`/server/api/`) only when necessary for:
- **Third-party API integrations** (payment processing, analytics)
- **Complex data aggregation** requiring server-side computation
- **Sensitive business logic** that shouldn't be exposed to clients
- **Rate limiting** and advanced caching strategies
- **SEO-critical data** requiring server-side rendering

### Data Fetching Patterns
```vue
<!-- Frontend: Direct Firebase access -->
<script setup>
const { currentProfile, profileLoading } = useCurrentProfile()
</script>

<!-- Backend: Server route for complex operations -->
<script setup>
const { data: analytics } = await $fetch('/api/profiles/analytics')
</script>
```

### Key Principles
1. **Default to Firebase** for simplicity and real-time capabilities
2. **Add server routes incrementally** as specific needs arise
3. **Keep sensitive operations server-side** (API keys, payment processing)
4. **Use composables** to abstract Firebase operations for consistency
5. **Document data patterns** in `/layers/[module]/docs/`

See `/layers/auth-module/docs/DATA_FETCHING_PATTERNS.md` for detailed examples.

## Firebase Setup and Configuration

### Environment Configuration
Each module that uses Firebase requires environment variables. See `/layers/auth-module/.env.example` for reference:
```bash
NUXT_PUBLIC_FIREBASE_API_KEY=
NUXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NUXT_PUBLIC_FIREBASE_PROJECT_ID=
NUXT_PUBLIC_FIREBASE_STORAGE_BUCKET=
NUXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NUXT_PUBLIC_FIREBASE_APP_ID=
NUXT_PUBLIC_FIREBASE_MEASUREMENT_ID=
NUXT_PUBLIC_USE_FIREBASE_EMULATOR=true  # Set to false for production
```

### Firebase Emulator Configuration
Local development uses Firebase emulators with these default ports:
- **Auth**: http://localhost:9099
- **Firestore**: http://localhost:8080
- **Storage**: http://localhost:9199
- **Functions**: http://localhost:5001
- **UI**: http://localhost:4000

### Security Rules
- **Firestore Rules**: `/firestore.rules`
- **Storage Rules**: `/storage.rules`
- Deploy with: `pnpm deploy:rules`

## Testing Strategy

### Test Coverage Requirements
- **Coverage Threshold**: 90% for statements, branches, functions, and lines
- **Test Types**: Unit, Integration, E2E, Performance, Security

### Running Tests
```bash
# Run all tests
pnpm test

# Run specific test types (from module directory)
cd layers/auth-module
pnpm test:unit          # Unit tests with Vitest
pnpm test:integration   # Integration tests
pnpm test:e2e          # E2E tests with Playwright
pnpm test:ui           # Interactive test UI

# Generate coverage report
pnpm coverage
```

## Authentication System

### Overview
The authentication system uses Firebase Auth with a multi-workspace architecture:
- Each user can have multiple workspaces
- Each workspace has exactly one profile per user
- User data is stored across multiple collections for flexibility

### Data Structure
```
users/{userId}
  - email, username, created_at, updated_at, is_active

workspaces/{workspaceId}
  - name, slug, description, logo_url, ownerId, created_by

workspace_members/{workspaceId}_{userId}
  - workspace_id, user_id, role (owner/admin/member)

profiles/{userId}_{workspaceId}
  - userId, workspace_id, display_name, bio, avatar_url
```

### Authentication Flow
1. **Signup**: Creates user → workspace → workspace_member → profile
2. **Login**: Loads user → finds workspaces → sets current workspace/profile
3. **Workspace Switch**: Updates current workspace and associated profile

### Key Composable: `useAuth()`
Located in `/layers/auth-module/composables/auth.ts`:
- Manages authentication state with cookies for persistence
- Handles signup, login, logout, and workspace switching
- Provides computed properties for permissions (isWorkspaceOwner, isWorkspaceAdmin)
- Integrates with Firebase Auth and Firestore

### Security Rules Considerations
- Field names must match between code and security rules (camelCase)
- Users can only create/update their own data
- Workspace members collection requires explicit rules
- Profile creation requires userId to match authenticated user

### Common Issues
1. **Permission Denied**: Check field name consistency (userId vs user_id)
2. **Missing Collections**: Ensure security rules exist for all collections
3. **Profile Not Found**: Verify profile document ID format: `{userId}_{workspaceId}`

## Important Development Notes

### Module Development
- All shared components/composables go in `layers/tairo/`
- Module-specific code stays within the module's directory
- Use TypeScript strict mode for all new code
- Follow existing component naming conventions (TairoPrefix)

### State Management
- Use Vue's reactivity system with composables
- No Vuex/Pinia by default - composables pattern preferred
- Firebase real-time listeners for live data sync

### Code Style
- ESLint auto-fixes on save (if configured in IDE)
- Follow existing patterns in the codebase
- Component composition over inheritance
- Minimal custom CSS - use Tailwind utilities

# CRITICAL FIREBASE RULES WARNING
**NEVER MODIFY FIREBASE SECURITY RULES (firestore.rules) - DO NOT TOUCH THIS FILE UNDER ANY CIRCUMSTANCES.**
The current rules allow all access in development and must remain unchanged.
